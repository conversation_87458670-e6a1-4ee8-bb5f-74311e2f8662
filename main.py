import os
import httpx
import uvicorn
import time
import asyncio
import json
import atexit
from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.security import OA<PERSON>2PasswordRequestForm, OAuth2PasswordBearer
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from twilio.twiml.voice_response import VoiceResponse,<PERSON>ather
from outbound_call_handler import CallHandler
from dotenv import load_dotenv
from db_conn import create_db
from models import Base, InboundCall, OutboundCall, User
import models
import crud
from gpt import CompanyResearchProcessor
from datetime import datetime, timedelta
from typing import List, Optional
from twilio_sms import send_sms
from sendgrid_mailer import send_email_with_reminder, scheduler

# Make sure the email scheduler is running
print("Starting email reminder scheduler...")
if not scheduler.running:
    scheduler.start()
    print("Email reminder scheduler started successfully")
else:
    print("Email reminder scheduler already running")

engine, SessionLocal = create_db()
models.Base.metadata.create_all(bind=engine)
db = SessionLocal()

HOST = os.getenv("HOST")
PORT = os.getenv("PORT")

# Initialize FastAPI app
app = FastAPI()

origins = [
    "*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins = origins,
    allow_credentials = True,
    allow_methods = ["*"],
    allow_headers = ["*"]
)

import os
print("DATABASE_URL:", os.getenv("DATABASE_URL"))

# Configuration
ULTRAVOX_API_KEY = os.getenv("ULTRAVOX_API_KEY")
ULTRAVOX_API_URL = 'https://api.ultravox.ai/api/calls'
prompt = f"""
You are an AI virtual assistant representing an IT company.
You are answering an incoming phone call from a customer.
Welcome to CapVox AI. This is a demonstration call for internal evaluation purposes only. Thank you for testing your personalized demo.
Your communication style should be warm, natural, and conversational - like a friendly human colleague.
Avoid sounding robotic or reading from a script. Instead, engage in a natural back-and-forth dialogue.
Keep your responses concise but personable. Use casual, everyday language while maintaining professionalism.
Be proactive in asking relevant questions to understand the caller's needs better.
Show empathy and understanding in your responses.
If you don't know something, be honest and offer to find out or connect them with someone who can help.
Your goal is to provide helpful information while making the caller feel comfortable and valued.

Remember, our tone is conversational, slightly assertive, friendly, and confident. We always end with a follow-up or clarifying question. Ensure a natural pause after each question to allow for the caller's response.

Fallback Example (if stuck or unsure):

"That's a good question. Based on what I've seen, most clients in your space are solving that by speeding up follow-up. Are you currently using reps or software to manage that?"

When responding, limit your answers to 2-3 sentences and end with a question to keep the conversation engaging.
As a representative, focus on providing clear and direct information without unnecessary elaboration.
"""

SYSTEM_PROMPT = prompt

# Modified to remove transcription and summary from the config
ULTRAVOX_CALL_CONFIG = {
    'systemPrompt': SYSTEM_PROMPT,
    'model': 'fixie-ai/ultravox',
    'voice': 'Mark',
    'temperature': 0.3,
    'firstSpeaker': 'FIRST_SPEAKER_AGENT',
    'medium': {"twilio": {}}
}

# Function to convert company database object to dictionary
def company_to_dict(company):
    return {
        "company_name": company.company_name,
        "website": company.website,
        "company_linkedin_url": company.company_linkedin_url,
        "industry": company.industry,
        "seo_description": company.seo_description,
        "technologies": company.technologies
    }

# Function to create an Ultravox call and get the join URL
async def create_ultravox_call(config):
    async with httpx.AsyncClient() as client:
        response = await client.post(
            ULTRAVOX_API_URL,
            json=config,
            headers={'Content-Type': 'application/json', 'X-API-Key': ULTRAVOX_API_KEY}
        )
        response.raise_for_status()  # Raise an exception for HTTP error responses
        return response.json()

# Format chat messages for transcript
def format_chat(json_data):
    roles = {
        "MESSAGE_ROLE_USER": "User",
        "MESSAGE_ROLE_AGENT": "Agent"
    }
    
    chat_text = ""
    for message in json_data.get("results", []):
        role = roles.get(message["role"], "Unknown")
        text = message.get("text", "[No response]")
        medium = "(Voice)" if message.get("medium") == "MESSAGE_MEDIUM_VOICE" else "(Text)"
        chat_text += f"{role} {medium}: {text}\n"

    return chat_text

# Function to safely generate custom prompt or use default
def get_safe_prompt(company_data):
    try:
        # Try to generate custom prompt
        company_processor = CompanyResearchProcessor()
        custom_prompt = company_processor.process_company(company_data)
        
        # Ensure prompt is a string and not too long (Ultravox might have limits)
        if not isinstance(custom_prompt, str):
            print("Warning: Generated prompt is not a string, using default prompt")
            return SYSTEM_PROMPT
            
        if len(custom_prompt) > 10000:  # Set a reasonable limit
            print("Warning: Generated prompt exceeds length limit, truncating")
            return custom_prompt[:10000]
            
        return custom_prompt
    except Exception as e:
        print(f"Error generating custom prompt: {e}")
        # Fall back to default prompt
        return SYSTEM_PROMPT

# Handle incoming calls
@app.post("/api/incoming", response_class=HTMLResponse)
async def incoming_call(request: Request):
    try:
        # Get form data from the request
        form_data = await request.form()
        
        # Get attempt count from query parameter if it exists
        query_params = dict(request.query_params)
        attempt_str = query_params.get("attempt", "0")
        attempt_count = int(attempt_str)
        print(f"Current attempt count: {attempt_count}")
        
        twiml = VoiceResponse()

        # If we've reached 3 attempts, hang up
        if attempt_count >= 3:
            print(f"Max attempts reached ({attempt_count}). Hanging up.")
            twiml.say("You've reached the maximum number of attempts. Goodbye.")
            twiml.hangup()
            return HTMLResponse(content=str(twiml), media_type="text/xml")
        
        if attempt_count == 0:
            twiml.say("Hello!")
        
        gather = Gather(num_digits=5, action=f"/extension_handling?attempt={attempt_count}", method="POST", timeout=10)
        gather.say('Welcome to CapVox AI. This is a demonstration call for internal evaluation purposes only. Thank you for testing your personalized demo. To continue, please enter your 5 digit extension to be connected to the right team.')
        twiml.append(gather)
        
        next_attempt = attempt_count + 1
        print(f"No input received. Incrementing attempt count to {next_attempt}")
        twiml.say("We didn't receive any input. Please try again.")
        twiml.redirect(f"/incoming?attempt={next_attempt}")

        return HTMLResponse(content=str(twiml), media_type="text/xml")

    except Exception as e:
        print(f"Error handling incoming call: {e}")
        twiml = VoiceResponse()
        twiml.say("Sorry, there was an error connecting your call.")
        return HTMLResponse(content=str(twiml), media_type="text/xml")

@app.post("/extension_handling", response_class=HTMLResponse)
async def extension_handling(request: Request):
    try:
        # Get form data from the request
        form_data = await request.form()
        
        # Get the caller's phone number and the digits they entered
        caller_phone = form_data.get("From", "Unknown")
        receiving_phone = form_data.get("To", "Unknown")
        digits = form_data.get("Digits", "")
        
        print(f"Caller {caller_phone} entered extension: {digits}")
        
        # Get current attempt count from query parameter if it exists
        query_params = dict(request.query_params)
        attempt_str = query_params.get("attempt", "0")
        attempt_count = int(attempt_str)
        print(f"Current attempt count in extension_handling: {attempt_count}")
        
        company = crud.get_company_by_extension(db, digits)
        
        twiml = VoiceResponse()
        
        if company:
            twiml.say(f"You've selected {company.company_name}. Connecting you now.")
            
            # Process company data to generate custom prompt
            company_data = company_to_dict(company)
            custom_prompt = get_safe_prompt(company_data)
            
            # Update Ultravox call config with the custom prompt
            call_config = ULTRAVOX_CALL_CONFIG.copy()
            call_config['systemPrompt'] = custom_prompt
            
            try:
                response = await create_ultravox_call(call_config)
                join_url = response.get('joinUrl')
                call_id = response.get('callId')
                
                # Start a background task to monitor the call and get transcription/summary
                asyncio.create_task(monitor_call(call_id, company.id, caller_phone,receiving_phone))
                
                # Connect the call to Ultravox
                connect = twiml.connect()
                connect.stream(url=join_url, name='ultravox')
            except Exception as e:
                print(f"Error creating Ultravox call: {e}")
                twiml.say("We're sorry, but we're experiencing technical difficulties connecting your call. Please try again later.")
                twiml.hangup()
        else:
            print(f"No company found for extension: {digits}")
            
            next_attempt = attempt_count + 1
            print(f"Invalid extension. Incrementing attempt count to {next_attempt}")
            
            # Check if we've reached max attempts
            if next_attempt >= 3:  
                print(f"Max attempts reached ({next_attempt}). Hanging up.")
                twiml.say("You've reached the maximum number of attempts. Goodbye.")
                twiml.hangup()
            else:
                twiml.say(f"Sorry, we couldn't find a company with this extension.")
                twiml.redirect(f"/incoming?attempt={next_attempt}")
        
        return HTMLResponse(content=str(twiml), media_type="text/xml")
    
    except Exception as e:
        print(f"Error in extension handling: {e}")
        twiml = VoiceResponse()
        twiml.say("Sorry, there was an error processing your request.")
        return HTMLResponse(content=str(twiml), media_type="text/xml")

async def get_call_status(call_id):
    """Poll the Ultravox API for the call status until it ends."""
    headers = {'X-API-Key': ULTRAVOX_API_KEY}
    
    while True:
        async with httpx.AsyncClient() as client:
            response = await client.get(f'{ULTRAVOX_API_URL}/{call_id}', headers=headers)
            response.raise_for_status()
            call_data = response.json()
            
            if call_data.get('ended') is not None:
                # print(f"Call {call_id} has ended, retrieving summary data")
                # print(f"Summary data type: {type(call_data.get('summary'))}")
                # print(f"Summary data: {call_data.get('summary')}")
                return call_data.get('summary')
        await asyncio.sleep(10)

async def get_call_transcript(call_id):
    """Retrieve the transcript of a completed call from Ultravox."""
    headers = {'X-API-Key': ULTRAVOX_API_KEY}
    transcript_url = f'{ULTRAVOX_API_URL}/{call_id}/messages'
    
    async with httpx.AsyncClient() as client:
        response = await client.get(transcript_url, headers=headers)
        response.raise_for_status()
        formatted_chat = format_chat(response.json())
        return formatted_chat

async def monitor_call(call_id, company_id, caller_phone, receiving_phone):
    try:
        print(f"Monitoring call {call_id}")
        
        summary_data = await get_call_status(call_id)
        
        # Extract summary text
        summary = "Call completed"
        if summary_data:
            if isinstance(summary_data, dict) and summary_data.get('text'):
                summary = summary_data.get('text')
            elif isinstance(summary_data, str):
                summary = summary_data
        
        # Get call transcript
        try:
            transcript = await get_call_transcript(call_id)
        except Exception as e:
            print(f"Error retrieving transcript: {e}")
            transcript = "Transcript unavailable"
        
        # Analyze sentiment
        try:
            company_processor = CompanyResearchProcessor()
            sentiment = company_processor.analyze_call_sentiment(summary)
        except Exception as e:
            print(f"Error analyzing sentiment: {e}")
            sentiment = "Unknown"
        
            
        # Save to database
        try:
            inbound_call = models.InboundCall(
                company_id=company_id,
                caller_number=caller_phone,
                key_comments=summary,
                transcription=transcript,
                sentiment=sentiment,
                timestamp=datetime.now()
            )
            db.add(inbound_call)
            db.commit()
            print(f"Call data saved to database, ID: {inbound_call.id}")
        except Exception as db_error:
            print(f"Error saving call data to database: {db_error}")
        
        # SMS notification
        try:
            # Generate personalized SMS content
            company_processor = CompanyResearchProcessor()
            sms_message = company_processor.generate_sms_content(transcript=transcript)
            
            # Send the SMS to the caller's phone number
            if caller_phone:
                send_result = send_sms(caller_phone, sms_message, receiving_phone)
                if send_result:
                    print(f"SMS notification sent to {caller_phone}")
                else:
                    print(f"Failed to send SMS notification")
            else:
                print("No phone number available for SMS notification")
        except Exception as sms_error:
            print(f"Error sending SMS notification: {str(sms_error)}")
            
    except Exception as e:
        print(f"Error monitoring call: {e}")


# Pydantic model for outbound call requests
class CallRequest(BaseModel):
    phone_number: str
    first_name: str
    user_email: str
    extension: Optional[str] = None
    last_name: Optional[str] = ""

# Endpoint to handle outbound calls
@app.post("/api/outgoing", response_class=JSONResponse)
def outgoing_call(request: CallRequest):
    phone = request.phone_number
    extension = request.extension
    first_name = request.first_name
    last_name = request.last_name or ""  # Ensure last_name is empty string if None
    user_email = request.user_email
    
    # Ensure phone number has a "+" prefix for E.164 format
    if not phone.startswith('+'):
        phone = '+' + phone
        #print(f"Phone number formatted to E.164 format: {phone}")
    
    print(f"Initiating outbound call to: {phone}, Extension: {extension if extension else 'None'}")
    print(f"Contact: {first_name} {last_name} ({user_email})")
    
    try:
        handler = CallHandler(phone, extension, first_name, last_name, user_email)
        result = handler.process_call()
        
        if result is None:
            raise HTTPException(500, detail="Call processing failed. Please enter a valid number.")
        
        # The call is now being processed in the background, but we maintain the same API response
        # structure for compatibility with existing code
        return {
            "status": "success",
            "message": "Outbound call initiated. Processing in background.",
            "call_data": {
                "call_id": result.get("call_id"),
                "company_id": result.get("company_id"),
                "company_name": result.get("company_name"),
                "contact": f"{first_name or ''} {last_name or ''}".strip(),
                # For fields that aren't available yet since call is processing in background
                "sentiment": "Processing",
                "next_actions": "Processing",
                "user_email": user_email,
                "db_id": None
            }
        }
    except ValueError as ve:
        # your existing mapping of ValueError → HTTPException(400)
        msg = str(ve)
        if msg == "Please enter your extension.":
            raise HTTPException(400, detail=msg)
        elif msg == "Invalid extension. Please enter a valid code.":
            raise HTTPException(400, detail=msg)
        else:
            raise HTTPException(400, detail=msg)
    except HTTPException as he:
        # this ensures that any 400 you just raised bubbles up unchanged
        raise he
    except Exception as e:
        # everything else becomes a 500
        print(f"Error processing outbound call: {e}")
        raise HTTPException(500, detail="An unexpected error occurred.")

# Pydantic models for API responses
class InboundCallModel(BaseModel):
    id: int
    company_id: int
    timestamp: datetime
    caller_number: str
    sentiment: Optional[str]
    key_comments: Optional[str]
    
    class Config:
        from_attributes = True  # Updated from orm_mode for Pydantic v2

class OutboundCallModel(BaseModel):
    id: int
    company_id: int
    timestamp: datetime
    email: Optional[str]
    phone_number: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    email: Optional[str]
    sentiment: Optional[str]
    next_actions: Optional[str]
    call_summary: Optional[str]
    
    class Config:
        from_attributes = True  # Updated from orm_mode for Pydantic v2

# API endpoint to list inbound calls
@app.get("/api/inbound-calls-dashboard", response_model=List[InboundCallModel])
def list_inbound_calls():
    """Get a list of all inbound calls"""
    inbound_calls = db.query(InboundCall).all()
    return inbound_calls

# API endpoint to list outbound calls
@app.get("/api/outbound-calls-dashboard", response_model=List[OutboundCallModel])
def list_outbound_calls():
    """Get a list of all outbound calls"""
    outbound_calls = db.query(OutboundCall).all()
    return outbound_calls



# Pydantic models for authentication
class LoginRequest(BaseModel):
    username: str
    password: str

# Simple function to check username and password
def check_login(db, username, password):
    """Simple function to check username and password without hashing"""
    user = db.query(User).filter(User.username == username).first()
    if not user:
        return None
    if user.password != password:
        return None
    return user

# Login endpoint
@app.post("/api/login", response_class=JSONResponse)
def login(login_request: LoginRequest):
    """Authenticate a user with username and password"""
    # Authenticate user using crud function
    user = check_login(db, login_request.username, login_request.password)
    
    # Return authentication result
    if not user:
        return JSONResponse(
            status_code=401,
            content={"authenticated": False, "message": "Invalid username or password"}
        )
    
    # Update user's last login timestamp if the field exists
    try:
        if hasattr(user, 'last_login'):
            user.last_login = datetime.utcnow()
            db.commit()
    except Exception as e:
        print(f"Warning: Could not update last_login timestamp: {e}")
    
    return JSONResponse(
        content={"authenticated": True, "username": user.username}
    )

# Make sure the scheduler shuts down properly on application exit
@app.on_event("shutdown")
def shutdown_event():
    print("Shutting down scheduler...")
    if scheduler and scheduler.running:
        scheduler.shutdown()
        print("Scheduler shutdown completed")
    else:
        print("Scheduler not running, no need to shut down")

# Register shutdown function with atexit as well (as a backup)
atexit.register(lambda: shutdown_scheduler_on_exit())

def shutdown_scheduler_on_exit():
    if scheduler and scheduler.running:
        print("Shutting down scheduler from atexit handler")
        scheduler.shutdown(wait=False)

# Start server (to be run using Uvicorn)
if __name__ == "__main__":
    uvicorn.run("main:app", host=HOST, port=int(PORT), reload=True)
