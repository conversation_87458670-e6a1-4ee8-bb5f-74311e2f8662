import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models import Base, Company
from decimal import Decimal, InvalidOperation

# Load CSV data
csv_path = "RD 17.4K with 1M Revenue-Grid view.csv"
df = pd.read_csv(csv_path)

# Create a database engine and session
DATABASE_URL = 'postgresql://postgres:postgres00@localhost:5432/calling_system'
engine = create_engine(DATABASE_URL)
Session = sessionmaker(bind=engine)
session = Session()

# Create tables if they don't exist
Base.metadata.create_all(engine)

# Add extension column starting from 00001
df = df.reset_index(drop=True)
df['extension'] = df.index + 1
df['extension'] = df['extension'].apply(lambda x: str(x).zfill(5))

def safe_float(val):
    try:
        return Decimal(val)
    except (ValueError, InvalidOperation, TypeError):
        return None

# Prepare and insert records
for _, row in df.iterrows():
    company = Company(
        extension=row['extension'],
        company_name=str(row.get('Company') or row.get('Company Name for Emails', '')).strip(),
        website=str(row.get('Website', '')).strip() or None,
        company_linkedin_url=str(row.get('Company Linkedin Url', '')).strip() or None,
        facebook_url=str(row.get('Facebook Url', '')).strip() or None,
        twitter_url=str(row.get('Twitter Url', '')).strip() or None,
        seo_description=str(row.get('SEO Description', '')).strip() or None,
        industry=str(row.get('Industry', '')).strip() or None,
        company_phone=str(row.get('Company Phone', '')).strip() or None,
        annual_revenue=safe_float(row.get('Annual Revenue')),
        city=str(row.get('City', '')).strip() or None,
        state=str(row.get('State', '')).strip() or None,
        country=str(row.get('Country', '')).strip() or None,
        technologies=str(row.get('Technologies', '')).strip() or None,
        contact_name=f"{str(row.get('First Name', '')).strip()} {str(row.get('Last Name', '')).strip()}".strip() or None,
        email=str(row.get('Email', '')).strip() or None
    )
    session.add(company)

# Commit session
session.commit()
session.close()

print("✅ CSV data successfully imported into PostgreSQL.")