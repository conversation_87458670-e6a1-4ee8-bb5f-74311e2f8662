import os
import time
import requests
import threading
import asyncio
from twilio.rest import Client
from dotenv import load_dotenv
from gpt import CompanyResearchProcessor
from datetime import datetime
from db_conn import create_db
from models import OutboundCall
from sqlalchemy.orm import Session
from twilio_sms import send_sms
from sendgrid_mailer import send_email_with_reminder, scheduler

load_dotenv()

class CallHandler:
    def __init__(self, phone_number: str, extension: str = None, first_name: str = None, last_name: str = None, user_email: str = None):
        # Validate required fields
        if not phone_number:
            raise ValueError("Phone number is required")
        if not first_name:
            raise ValueError("First name is required")
        if not user_email:
            raise ValueError("Email is required")
        if not extension:
            raise ValueError("Please enter your extension.")
            
        # Phone number to call
        self.destination_phone_number = phone_number
        self.extension = extension
        # Contact info
        self.first_name = first_name
        self.last_name = last_name or ""  # Ensure last_name is empty string if None
        self.user_email = user_email
        
        # Twilio configuration
        self.twilio_account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        #print(f"TWILIO_ACCOUNT_SID: {self.twilio_account_sid}")
        self.twilio_auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        #print(f"TWILIO_AUTH_TOKEN: {self.twilio_auth_token}")
        self.twilio_phone_number = os.getenv('TWILIO_PHONE_NUMBER')
        #print(f"TWILIO_PHONE_NUMBER: {self.twilio_phone_number}")
                              
        # Ultravox configuration
        self.ultravox_api_key = os.getenv('ULTRAVOX_API_KEY')
        self.ultravox_api_url = 'https://api.ultravox.ai/api/calls'
        
        # Database connection
        _, self.SessionLocal = create_db()
        self.db = self.SessionLocal()
        
        # Initialize company data
        self.company = None
        self.company_data = {}
        
        # Validate extension exists in database
        if not self.load_company_by_extension(extension):
            raise ValueError("Invalid extension. Please enter a valid code.")
        
        # Generate system prompt based on company data
        self.system_prompt = self.generate_prompt()
        
        # Configure Ultravox call
        self.ultravox_call_config = {
            'systemPrompt': self.system_prompt,
            'model': 'fixie-ai/ultravox',
            'voice': 'Mark',
            'temperature': 0.5,
            'firstSpeaker': 'FIRST_SPEAKER_AGENT',
            'medium': {'twilio': {}},
            'transcriptOptional': False,
        }
        
        # Initialize Twilio client
        self.twilio_client = Client(self.twilio_account_sid, self.twilio_auth_token)
    
    def load_company_by_extension(self, extension):
        """Load company details from the database using extension"""
        try:
            # Import here to avoid circular imports
            import crud
            
            # Get company from database
            self.company = crud.get_company_by_extension(self.db, extension)
            
            if self.company:
                print(f"Found company: {self.company.company_name} (ID: {self.company.id})")
                
                # Store company data for prompt generation
                self.company_data = {
                    "company_name": self.company.company_name,
                    "website": self.company.website,
                    "company_linkedin_url": self.company.company_linkedin_url,
                    "industry": self.company.industry,
                    "seo_description": self.company.seo_description,
                    "technologies": self.company.technologies
                }
                return True
            else:
                print(f"No company found for extension: {extension}")
                return False
        except Exception as e:
            print(f"Error loading company data: {str(e)}")
            return False
    
    def company_to_dict(self):
        """Convert company object to dictionary for GPT processing"""
        if not self.company:
            return {}
        
        return {
            "company_name": self.company.company_name,
            "website": self.company.website,
            "company_linkedin_url": self.company.company_linkedin_url,
            "industry": self.company.industry,
            "seo_description": self.company.seo_description,
            "technologies": self.company.technologies
        }
    
    def generate_prompt(self):
        """Generate a structured prompt based on company data"""
        # If we have company data, use GPT to create a custom prompt
        if self.company and self.company_data:
            try:
                # Use CompanyResearchProcessor for enhanced company prompt
                processor = CompanyResearchProcessor()
                custom_prompt = processor.process_company(self.company_data)
                
                # Add outbound call context to the prompt
                greeting_instruction = ""
                if self.first_name and self.last_name:
                    greeting_instruction = f'Start the call by greeting the user with their first name and last name: "{self.first_name} {self.last_name}"\nFor example: "Hi {self.first_name} {self.last_name}, it\'s a demo call from CapVox AI for internal evaluation purposes only.Thank you for testing your personalized demo. This is [your name] from [company name]. How are you today?"'
                elif self.first_name:
                    greeting_instruction = f'Start the call by greeting the user with their first name: "{self.first_name}"\nFor example: "Hi {self.first_name}, it\'s a demo call from CapVox AI for internal evaluation purposes only.Thank you for testing your personalized demo. This is [your name] from [company name]. How are you today?"'
                elif self.last_name:
                    greeting_instruction = f'Start the call by greeting the user with Mr./Ms. and their last name: "{self.last_name}"\nFor example: "Hi Mr./Ms. {self.last_name}, it\'s a demo call from CapVox AI for internal evaluation purposes only.Thank you for testing your personalized demo. This is [your name] from [company name]. How are you today?"'
                else:
                    greeting_instruction = 'Start the call with a generic greeting.\nFor example: "Hi there, it\'s a demo call from CapVox AI for internal evaluation purposes only.Thank you for testing your personalized demo. This is [your name] from [company name]. How are you today?"'
                
                outbound_context = f"""
You are an AI virtual assistant representing an IT company.
You are making a phone call to assist a customer.
Welcome to CapVox AI. This is a demonstration call for internal evaluation purposes only. Thank you for testing your personalized demo.
{greeting_instruction}
Your communication style should be warm, natural, and conversational - like a friendly human colleague.
Avoid sounding robotic or reading from a script. Instead, engage in a natural back-and-forth dialogue.
Keep your responses concise but personable. Use casual, everyday language while maintaining professionalism.
Be proactive in asking relevant questions to understand the customer's needs better.
Show empathy and understanding in your responses.
If you don't know something, be honest and offer to find out or connect them with someone who can help.
Your goal is to provide helpful information while making the customer feel comfortable and valued.

Remember, our tone is conversational, slightly assertive, friendly, and confident. We always end with a follow-up or clarifying question. Ensure a natural pause after each question to allow for the caller's response.

Fallback Example (if stuck or unsure):

"That's a good question. Based on what I've seen, most clients in your space are solving that by speeding up follow-up. Are you currently using reps or software to manage that?"

When responding, limit your answers to 2-3 sentences and end with a question to keep the conversation engaging.
As a representative, focus on providing clear and direct information without unnecessary elaboration.
"""
                custom_prompt += outbound_context
                
                #print("Generated custom company prompt for outbound call")
                return custom_prompt
            except Exception as e:
                print(f"Error generating custom prompt: {str(e)}")
                # Fall back to default prompt
        
        # Default generic prompt if no company data or error
        greeting_instruction = ""
        if self.first_name and self.last_name:
            greeting_instruction = f'Start the call by greeting the user with their first name and last name: "{self.first_name} {self.last_name}"\nFor example: "Hi {self.first_name} {self.last_name}, it\'s a demo call from CapVox AI for internal evaluation purposes only.Thank you for testing your personalized demo. This is [your name] from [company name]. How are you today?"'
        elif self.first_name:
            greeting_instruction = f'Start the call by greeting the user with their first name: "{self.first_name}"\nFor example: "Hi {self.first_name}, it\'s a demo call from CapVox AI for internal evaluation purposes only.Thank you for testing your personalized demo. This is [your name] from [company name]. How are you today?"'
        elif self.last_name:
            greeting_instruction = f'Start the call by greeting the user with Mr./Ms. and their last name: "{self.last_name}"\nFor example: "Hi Mr./Ms. {self.last_name}, it\'s a demo call from CapVox AI for internal evaluation purposes only.Thank you for testing your personalized demo. This is [your name] from [company name]. How are you today?"'
        else:
            greeting_instruction = 'Start the call with a generic greeting.\nFor example: "Hi there, it\'s a demo call from CapVox AI for internal evaluation purposes only.Thank you for testing your personalized demo. This is [your name] from [company name]. How are you today?"'
        
        default_prompt = f"""
You are an AI virtual assistant representing an IT company.
You are making a phone call to assist a customer.
{greeting_instruction}
Your communication style should be warm, natural, and conversational - like a friendly human colleague.
Avoid sounding robotic or reading from a script. Instead, engage in a natural back-and-forth dialogue.
Keep your responses concise but personable. Use casual, everyday language while maintaining professionalism.
Be proactive in asking relevant questions to understand the customer's needs better.
Show empathy and understanding in your responses.
If you don't know something, be honest and offer to find out or connect them with someone who can help.
Your goal is to provide helpful information while making the customer feel comfortable and valued.

Remember, our tone is conversational, slightly assertive, friendly, and confident. We always end with a follow-up or clarifying question. Ensure a natural pause after each question to allow for the caller's response.

Fallback Example (if stuck or unsure):

"That's a good question. Based on what I've seen, most clients in your space are solving that by speeding up follow-up. Are you currently using reps or software to manage that?"

When responding, limit your answers to 2-3 sentences and end with a question to keep the conversation engaging.
As a representative, focus on providing clear and direct information without unnecessary elaboration.
"""
        return default_prompt.strip()
    
    def create_ultravox_call(self):
        """Initiate a call request to Ultravox API."""
        headers = {
            'Content-Type': 'application/json',
            'X-API-Key': self.ultravox_api_key
        }
        # Print Ultravox config for debugging
        #print(f"Ultravox config: {self.ultravox_call_config}")
        
        response = requests.post(self.ultravox_api_url, json=self.ultravox_call_config, headers=headers)
        response.raise_for_status()
        return response.json()
    
    def initiate_twilio_call(self, join_url):
        """Initiate the call using Twilio by connecting it to Ultravox."""
        call = self.twilio_client.calls.create(
            twiml=f'<Response><Connect><Stream url="{join_url}"/></Connect></Response>',
            to=self.destination_phone_number,
            from_=self.twilio_phone_number
        )
        return call.sid
    
    def get_call_status(self, call_id):
        """Poll the Ultravox API for the call status until it ends."""
        headers = {'X-API-Key': self.ultravox_api_key}
        
        while True:
            response = requests.get(f'{self.ultravox_api_url}/{call_id}', headers=headers)
            response.raise_for_status()
            call_data = response.json()
            
            if call_data.get('ended') is not None:
                return call_data.get('summary')
            time.sleep(10)

    def format_chat(self, json_data):
        roles = {
            "MESSAGE_ROLE_USER": "User",
            "MESSAGE_ROLE_AGENT": "Agent"
        }
        
        chat_text = ""
        for message in json_data["results"]:
            role = roles.get(message["role"], "Unknown")
            text = message.get("text", "[No response]")
            medium = "(Voice)" if message.get("medium") == "MESSAGE_MEDIUM_VOICE" else "(Text)"
            chat_text += f"{role} {medium}: {text}\n"
    
        return chat_text
    
    def get_call_transcript(self, call_id):
        """Retrieve the transcript of a completed call from Ultravox."""
        headers = {'X-API-Key': self.ultravox_api_key}
        transcript_url = f'{self.ultravox_api_url}/{call_id}/messages'
        
        response = requests.get(transcript_url, headers=headers)
        response.raise_for_status()
        formatted_chat = self.format_chat(response.json())
        return formatted_chat
    
    def analyze_sentiment(self, summary_text):
        """Analyze the sentiment of the call summary using GPT."""
        try:
            sentiment = CompanyResearchProcessor().analyze_call_sentiment(summary_text)
            print(f'Call sentiment: {sentiment}')
            return sentiment
        except Exception as e:
            print(f'Error analyzing sentiment: {str(e)}')
            return "Unknown"
    
    def save_to_database(self, call_id, summary, transcript, sentiment, next_actions):
        """Save call data to the outbound_calls table."""
        try:
            # Get company ID and details
            company_id = self.company.id if self.company else 1
            email = self.user_email
            
            # Use provided first_name and last_name
            first_name = self.first_name
            last_name = self.last_name  
            
            # If both first_name and last_name are not provided, try to get from company
            if not self.first_name and not self.last_name and self.company and hasattr(self.company, 'contact_name') and self.company.contact_name:
                # If contact name is available, try to split it into first and last name
                contact_parts = self.company.contact_name.split(' ', 1)
                if len(contact_parts) > 0:
                    first_name = contact_parts[0]
                if len(contact_parts) > 1:
                    last_name = contact_parts[1]
            
            # Ensure values are not None before saving to database
            summary = summary if summary is not None else "No summary available"
            sentiment = sentiment if sentiment is not None else "N/A"
            next_actions = next_actions if next_actions is not None else "N/A"
            
            # Create outbound call record
            outbound_call = OutboundCall(
                company_id=company_id,
                phone_number=self.destination_phone_number,
                first_name=first_name,
                last_name=last_name,
                email=email,
                call_summary=summary,
                transcription=transcript,
                sentiment=sentiment,
                next_actions=next_actions,
                timestamp=datetime.now()
            )
            
            # Add to database and commit
            self.db.add(outbound_call)
            self.db.commit()
            
            print(f'Call data saved to database, ID: {outbound_call.id}')
            return outbound_call.id
        except Exception as e:
            self.db.rollback()
            print(f'Error saving to database: {str(e)}')
            return None
    
    def generate_next_actions(self, transcript):
        """Generate next actions using GPT from the call transcript."""
        try:
            next_actions = CompanyResearchProcessor().generate_next_actions(transcript)
            #print(f'Generated next actions: {next_actions}')
            return next_actions
        except Exception as e:
            print(f'Error generating next actions: {str(e)}')
            return "Follow up required based on call content"
    
    def process_call_in_background(self, call_id, join_url):
        """Process call in background thread, allowing the API to return immediately"""
        db_session = None
        try:
            # Create a new database session for this thread
            _, SessionLocal = create_db()
            db_session = SessionLocal()
            
            # Wait for call to complete
            summary_data = self.get_call_status(call_id)
            
            # Check if call was accepted based on summary data
            call_accepted = summary_data is not None
            
            # Extract summary text
            if call_accepted:
                summary = "Call completed"
                if summary_data:
                    if isinstance(summary_data, dict) and summary_data.get('text'):
                        summary = summary_data.get('text')
                    elif isinstance(summary_data, str):
                        summary = summary_data
                
                # Get transcript
                try:
                    transcript = self.get_call_transcript(call_id)
                except Exception as e:
                    print(f"Error retrieving transcript: {e}")
                    transcript = "Transcript unavailable"
                    
                # Analyze sentiment
                sentiment = self.analyze_sentiment(summary)
                
                # Generate next actions
                next_actions = self.generate_next_actions(transcript)
            else:
                summary = "Call not accepted"
                sentiment = "N/A"
                next_actions = "N/A"
                transcript = "Call not accepted - no transcript available"
            
            # Save to database
            db_id = self.save_to_database(call_id, summary, transcript, sentiment, next_actions)
            
            # Only send SMS and email notifications if call was accepted
            if call_accepted:
                # Send SMS notification after call is completed
                try:
                    # Generate personalized SMS content
                    processor = CompanyResearchProcessor()
                    sms_message = processor.generate_sms_content(transcript=transcript)
                    
                    # Send the SMS
                    if self.destination_phone_number:
                        from_number = os.getenv("TWILIO_PHONE_NUMBER")
                        send_result = send_sms(self.destination_phone_number, sms_message, from_number)
                        if send_result:
                            print(f"SMS notification sent to {self.destination_phone_number}")
                        else:
                            print(f"Failed to send SMS notification")
                    else:
                        print("No phone number available for SMS notification")
                        
                except Exception as sms_error:
                    print(f"Error sending SMS notification: {str(sms_error)}")
                
                # Send follow-up email after SMS (whether SMS was sent successfully or not)
                try:
                    if self.user_email:
                        # Generate personalized email content using GPT
                        processor = CompanyResearchProcessor()
                        email_content = processor.generate_email_content_outbound(
                            first_name=self.first_name,
                            last_name=self.last_name,
                            transcript=transcript
                        )
                        
                        # Send the email with a reminder after 24 hours
                        email_subject = "Follow-up from your CapVox Conversation"
                        print(f"Sending follow-up email to {self.user_email} with a reminder scheduled in 24 hours")
                        email_sent = send_email_with_reminder(
                            to_email=self.user_email, 
                            subject=email_subject, 
                            content=email_content,
                            reminder_minutes=1440,  # 24 hours = 1440 minutes
                            first_name=self.first_name,
                            last_name=self.last_name,
                            transcript=transcript
                        )
     
                        if email_sent:
                            print("------------------------------")
                            print(f"Follow-up email sent successfully to {self.user_email} with reminder scheduled")
                        else:
                            print(f"Failed to send follow-up email to {self.user_email}")
                    else:
                        print("No email address available for follow-up email")
                except Exception as email_error:
                    print(f"Error sending follow-up email: {str(email_error)}")
            else:
                print(f"Call {call_id} was not accepted. Skipping SMS and email notifications.")
            
        except Exception as e:
            print(f'Error in background process: {str(e)}')
        finally:
            # Close database connection
            if db_session is not None:
                db_session.close()
            if hasattr(self, 'db') and self.db is not None:
                self.db.close()
    
    def process_call(self):
        """Main function to handle the complete call flow."""
        try:
            #print('Creating Ultravox call...')
            response = self.create_ultravox_call()
            
            join_url = response.get('joinUrl')
            call_id = response.get('callId')
            
            if not join_url or not call_id:
                raise ValueError("Missing required fields in API response")
            
            # print(f'Join URL: {join_url}')
            # print(f'Call ID: {call_id}')
            
            call_sid = self.initiate_twilio_call(join_url)
            print(f'Outbound Call initiated: {call_sid}')
            
            # Start a background thread to process the call
            thread = threading.Thread(
                target=self.process_call_in_background,
                args=(call_id, join_url),
                daemon=True
            )
            thread.start()
            print(f'Background processing started for call {call_id}')
            
            # Return immediately with call info
            
            return {
                'call_id': call_id,
                'company_id': self.company.id if self.company else None,
                'company_name': self.company.company_name if self.company else None,
                'first_name': self.first_name,
                'last_name': self.last_name,
                'user_email': self.user_email,
                'status': 'initiated',
                'message': 'Call initiated successfully. Processing in background.'
            }
        except Exception as e:
            print(f'Error initiating call: {str(e)}')
            if hasattr(self, 'db'):
                self.db.close()
            return None

# if __name__ == '__main__':
#     call_handler = CallHandler()
#     call_handler.process_call()