from sqlalchemy import Column, Integer, String, Text, ForeignKey, Numeric, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password = Column(String(256), nullable=False)  # Store hashed password

class Company(Base):
    __tablename__ = 'companies'

    id = Column(Integer, primary_key=True)
    extension = Column(String(5), unique=True, nullable=False)
    company_name = Column(Text, nullable=False)
    website = Column(Text)
    company_linkedin_url = Column(Text)
    facebook_url = Column(Text)
    twitter_url = Column(Text)
    seo_description = Column(Text)
    industry = Column(Text)
    company_phone = Column(Text)
    annual_revenue = Column(Numeric)
    city = Column(Text)
    state = Column(Text)
    country = Column(Text)
    technologies = Column(Text)
    contact_name = Column(Text)
    email = Column(Text)

    inbound_calls = relationship("InboundCall", back_populates="company")
    outbound_calls = relationship("OutboundCall", back_populates="company")


class InboundCall(Base):
    __tablename__ = 'inbound_calls'

    id = Column(Integer, primary_key=True)
    company_id = Column(Integer, ForeignKey('companies.id'))
    timestamp = Column(DateTime, default=datetime.utcnow)
    caller_number = Column(Text)
    sentiment = Column(Text) #pass summary to gpt strucutred output. (in gpt.py-> make class and use it as a object)
    key_comments = Column(Text) #summary
    transcription = Column(Text)
    
    company = relationship("Company", back_populates="inbound_calls")


class OutboundCall(Base):
    __tablename__ = 'outbound_calls'

    id = Column(Integer, primary_key=True)
    company_id = Column(Integer, ForeignKey('companies.id'))
    timestamp = Column(DateTime, default=datetime.utcnow)
    first_name = Column(Text)
    last_name = Column(Text)
    phone_number = Column(Text)
    email = Column(Text)
    sentiment = Column(Text)
    call_summary = Column(Text)
    next_actions = Column(Text) # transcription -> Company's next actions
    transcription = Column(Text)

    company = relationship("Company", back_populates="outbound_calls")