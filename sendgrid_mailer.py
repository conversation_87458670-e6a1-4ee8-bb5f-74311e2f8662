import sendgrid
import os
import re
import logging
from sendgrid.helpers.mail import Mail, Email, To, Content
from dotenv import load_dotenv
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from datetime import datetime, timedelta
import time

# Configure basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize the scheduler with SQLite storage for persistence
jobstores = {
    'default': SQLAlchemyJobStore(url='sqlite:///scheduler.sqlite')
}

scheduler = BackgroundScheduler(jobstores=jobstores)
scheduler.start()

def send_email(to_email, subject, content):
    """
    Simple function to send an email using SendGrid
    
    Args:
        to_email (str): Recipient email address
        subject (str): Email subject
        content (str): Email content (plain text)
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get API key from environment
        api_key = os.getenv('SENDGRID_API_KEY')
        if not api_key:
            logger.error("SENDGRID_API_KEY not found in environment variables")
            return False
            
        # Get sender email from environment
        from_email_address = os.getenv('SENDGRID_VERIFIED_SENDER', '<EMAIL>')
        
        # Create email
        from_email = Email(from_email_address)
        to_email = To(to_email)
        content = Content("text/plain", content)
        mail = Mail(from_email, to_email, subject, content)
        
        # Send email
        logger.info(f"Sending email to {to_email}")
        sg = sendgrid.SendGridAPIClient(api_key=api_key)
        response = sg.client.mail.send.post(request_body=mail.get())
        
        # Check response
        if response.status_code >= 200 and response.status_code < 300:
            logger.info(f"Email sent successfully: {response.status_code}")
            return True
        else:
            logger.error(f"Failed to send email. Status code: {response.status_code}")
            return False
        
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        return False

def send_reminder_email(to_email, subject, content, first_name=None, last_name=None, transcript=None):
    """
    Send a reminder email to the specified recipient
    
    Args:
        to_email (str): Recipient email address
        subject (str): Email subject
        content (str): Original email content (plain text)
        first_name (str, optional): Recipient's first name for personalized reminder
        last_name (str, optional): Recipient's last name for personalized reminder
        transcript (str, optional): Call transcript for context in reminder
    """
    reminder_subject = f"Reminder: {subject}"
    reminder_content = ""
    
    try:
        # Import the processor to generate reminder email content
        from gpt import CompanyResearchProcessor
        processor = CompanyResearchProcessor()
        
        # Generate personalized reminder content using the transcript
        reminder_content = processor.generate_reminder_email_content(
            first_name=first_name,
            last_name=last_name,
            transcript=transcript
        )
        
        # If generation fails, fall back to the simple reminder format
        if not reminder_content:
            reminder_content = f"Just a friendly reminder about our conversation.\n\n{content}\n\nLooking forward to connecting,\nCapVox AI"
    except Exception as e:
        logger.error(f"Error generating personalized reminder content: {str(e)}")
        
    logger.info(f"Sending reminder email to {to_email}")
    send_email(to_email, reminder_subject, reminder_content)

def schedule_reminder_email(to_email, subject, content, minutes=1, first_name=None, last_name=None, transcript=None):
    """
    Schedule a reminder email to be sent after specified minutes
    
    Args:
        to_email (str): Recipient email address
        subject (str): Email subject
        content (str): Email content (plain text)
        minutes (int): Minutes to wait before sending reminder (default: 1)
        first_name (str, optional): Recipient's first name for personalized reminder
        last_name (str, optional): Recipient's last name for personalized reminder
        transcript (str, optional): Call transcript for context in reminder
    """
    run_time = datetime.now() + timedelta(minutes=minutes)
    logger.info(f"Scheduling reminder email to {to_email} at {run_time}")
    
    job_id = f"reminder_email_{to_email}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    scheduler.add_job(
        send_reminder_email, 
        'date', 
        run_date=run_time, 
        args=[to_email, subject, content, first_name, last_name, transcript],
        id=job_id,
        replace_existing=True
    )
    
    # Check if scheduler is running, if not, start it
    if not scheduler.running:
        logger.warning("Scheduler was not running. Starting scheduler now.")
        scheduler.start()
        
    logger.info(f"Job {job_id} scheduled to run at {run_time}")
    return job_id

def send_email_with_reminder(to_email, subject, content, reminder_minutes=1, first_name=None, last_name=None, transcript=None):
    """
    Send an email and schedule a reminder
    
    Args:
        to_email (str): Recipient email address
        subject (str): Email subject
        content (str): Email content (plain text)
        reminder_minutes (int): Minutes to wait before sending reminder (default: 1)
        first_name (str, optional): Recipient's first name for personalized reminder
        last_name (str, optional): Recipient's last name for personalized reminder
        transcript (str, optional): Call transcript for context in reminder
        
    Returns:
        bool: True if initial email was successful, False otherwise
    """
    logger.info(f"Sending email with reminder to {to_email}, reminder in {reminder_minutes} minute(s)")
    
    # Send initial email
    initial_result = send_email(to_email, subject, content)
    
    # Schedule reminder only if initial email was successful
    if initial_result:
        job_id = schedule_reminder_email(
            to_email, 
            subject, 
            content, 
            reminder_minutes, 
            first_name, 
            last_name, 
            transcript
        )
        logger.info(f"Reminder email scheduled with job ID: {job_id}")
    
    return initial_result

# If running this file directly, run a simple test
if __name__ == "__main__":
    print("Running SendGrid mailer test with automatic reminder...")
    
    # Start scheduler if not running
    if not scheduler.running:
        print("Starting scheduler...")
        scheduler.start()
    
    # Create a fake call transcript for testing
    fake_transcript = """
    AI: Hello, this is CapVox AI calling on behalf of Tech Solutions. Am I speaking with John?
    Customer: Yes, this is John.
    AI: Great! I'm calling to discuss how our AI-powered analytics platform could help improve your company's data processing efficiency. Would you be interested in learning more?
    Customer: Sure, I've been looking for solutions to streamline our data analysis.
    AI: Excellent! Our platform has helped similar companies reduce processing time by 40% and increase accuracy by 25%. Would you be available for a quick demo next week?
    Customer: That sounds promising. Yes, I could make time for a demo.
    AI: Perfect! I'll have our team reach out to schedule. Do you have any specific questions I can address now?
    Customer: What about integration with our existing systems?
    AI: Great question. Our platform is designed with flexible APIs that connect with most major systems. We can discuss your specific setup during the demo.
    Customer: Sounds good, looking forward to it.
    """
    
    # Test email with the fake transcript
    test_email = "<EMAIL>"  # Replace with your email
    test_subject = "Follow-up from your CapVox Conversation"
    
    # Import the processor to generate email content
    from gpt import CompanyResearchProcessor
    processor = CompanyResearchProcessor()
    
    # Generate email content using the fake transcript
    email_content = processor.generate_email_content_outbound(
        first_name="John",
        last_name="Doe",
        transcript=fake_transcript
    )
    
    # Test option 1: Send the test email with a reminder after 1 minute
    print(f"Sending test email to {test_email} with reminder after 1 minute (production setting: 24 hours)")
    result = send_email_with_reminder(
        to_email=test_email, 
        subject=test_subject, 
        content=email_content, 
        reminder_minutes=1,  # Using 1 minute for testing (production uses 1440 minutes = 24 hours)
        first_name="John",
        last_name="Doe",
        transcript=fake_transcript
    )
    
    if result:
        print("✅ Test email sent successfully with reminder scheduled!")
        print(f"Email content preview: {email_content[:100]}...")
        print("\nWaiting for reminder to be sent (keep this script running)...")
    else:
        print("❌ Failed to send test email.")
        
    # Keep the script running so scheduler can execute
    try:
        print("\nPress Ctrl+C to exit...")
        while True:
            time.sleep(5)
            print(".", end="", flush=True)
    except KeyboardInterrupt:
        print("\nShutting down scheduler...")
        scheduler.shutdown()
        print("Test completed.")