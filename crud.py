from sqlalchemy.orm import Session
from sqlalchemy import exc
from models import Company, User, InboundCall, OutboundCall
from typing import List, Optional, Dict, Any
import logging
from passlib.context import CryptContext

# Set up password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Company CRUD operations
def create_company(db: Session, company_data: Dict[str, Any]) -> Optional[Company]:
    """Create a new company record"""
    try:
        db_company = Company(**company_data)
        db.add(db_company)
        db.commit()
        db.refresh(db_company)
        return db_company
    except exc.IntegrityError as e:
        db.rollback()
        logger.error(f"Integrity error creating company: {str(e)}")
        return None
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating company: {str(e)}")
        return None

def get_company(db: Session, company_id: int) -> Optional[Company]:
    """Get a company by ID"""
    return db.query(Company).filter(Company.id == company_id).first()

def get_company_by_id(db: Session, company_id: int) -> Optional[Company]:
    """Get a company by ID (alias for get_company)"""
    return get_company(db, company_id)

def get_company_by_extension(db: Session, extension: str) -> Optional[Company]:
    """Get a company by extension"""
    return db.query(Company).filter(Company.extension == extension).first()

def get_companies(db: Session, skip: int = 0, limit: int = 100) -> List[Company]:
    """Get a list of companies with pagination"""
    return db.query(Company).offset(skip).limit(limit).all()

def update_company(db: Session, company_id: int, company_data: Dict[str, Any]) -> Optional[Company]:
    """Update a company record"""
    try:
        db_company = get_company(db, company_id)
        if db_company:
            for key, value in company_data.items():
                setattr(db_company, key, value)
            db.commit()
            db.refresh(db_company)
            return db_company
        return None
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating company: {str(e)}")
        return None

def delete_company(db: Session, company_id: int) -> bool:
    """Delete a company record"""
    try:
        db_company = get_company(db, company_id)
        if db_company:
            db.delete(db_company)
            db.commit()
            return True
        return False
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting company: {str(e)}")
        return False

# User CRUD operations
def create_user(db: Session, username: str, password: str) -> Optional[User]:
    """Create a new user with hashed password"""
    try:
        # Check if user already exists
        existing_user = get_user_by_username(db, username)
        if existing_user:
            logger.warning(f"User with username {username} already exists")
            return None
        
        # Hash the password
        hashed_password = pwd_context.hash(password)
        
        # Create the user
        db_user = User(
            username=username,
            password=hashed_password
        )
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating user: {str(e)}")
        return None

def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """Get a user by username"""
    return db.query(User).filter(User.username == username).first()

def get_user(db: Session, user_id: int) -> Optional[User]:
    """Get a user by ID"""
    return db.query(User).filter(User.id == user_id).first()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify that the password matches the hash"""
    return pwd_context.verify(plain_password, hashed_password)

def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """Authenticate a user by username and password"""
    user = get_user_by_username(db, username)
    if not user:
        return None
    if not verify_password(password, user.password):
        return None
    return user

def update_password(db: Session, user_id: int, new_password: str) -> bool:
    """Update a user's password"""
    try:
        user = get_user(db, user_id)
        if not user:
            return False
        
        # Hash the new password
        hashed_password = pwd_context.hash(new_password)
        user.password = hashed_password
        
        db.commit()
        return True
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating password: {str(e)}")
        return False

# InboundCall CRUD operations
def get_inbound_call(db: Session, call_id: int) -> Optional[InboundCall]:
    """Get an inbound call by ID"""
    return db.query(InboundCall).filter(InboundCall.id == call_id).first()

def get_inbound_calls(db: Session, skip: int = 0, limit: int = 100) -> List[InboundCall]:
    """Get a list of inbound calls with pagination"""
    return db.query(InboundCall).order_by(InboundCall.timestamp.desc()).offset(skip).limit(limit).all()

def get_inbound_calls_by_company(db: Session, company_id: int) -> List[InboundCall]:
    """Get all inbound calls for a specific company"""
    return db.query(InboundCall).filter(InboundCall.company_id == company_id).order_by(InboundCall.timestamp.desc()).all()

# OutboundCall CRUD operations
def get_outbound_call(db: Session, call_id: int) -> Optional[OutboundCall]:
    """Get an outbound call by ID"""
    return db.query(OutboundCall).filter(OutboundCall.id == call_id).first()

def get_outbound_calls(db: Session, skip: int = 0, limit: int = 100) -> List[OutboundCall]:
    """Get a list of outbound calls with pagination"""
    return db.query(OutboundCall).order_by(OutboundCall.timestamp.desc()).offset(skip).limit(limit).all()

def get_outbound_calls_by_company(db: Session, company_id: int) -> List[OutboundCall]:
    """Get all outbound calls for a specific company"""
    return db.query(OutboundCall).filter(OutboundCall.company_id == company_id).order_by(OutboundCall.timestamp.desc()).all() 
    