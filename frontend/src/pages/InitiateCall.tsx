import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card';
import { callApi } from '../services/api';
import type { CallFormData, ApiError } from '../types/api';
import { isValidEmail, isValidPhoneNumber, formatPhoneNumber } from '../utils';
import { Phone, CheckCircle, AlertCircle, ArrowLeft } from 'lucide-react';

const InitiateCall: React.FC = () => {
  const [formData, setFormData] = useState<CallFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    extension: '',
  });
  
  const [errors, setErrors] = useState<Partial<CallFormData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);

  const validateForm = (): boolean => {
    const newErrors: Partial<CallFormData> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!isValidEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    } else if (!isValidPhoneNumber(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof CallFormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
    
    // Clear API error when user makes changes
    if (apiError) {
      setApiError(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setApiError(null);

    try {
      const callRequest = {
        phone_number: formData.phoneNumber,
        first_name: formData.firstName,
        last_name: formData.lastName,
        user_email: formData.email,
        extension: formData.extension || undefined,
      };

      const response = await callApi.initiateCall(callRequest);
      
      if (response.success) {
        setSuccess(true);
        // Reset form
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phoneNumber: '',
          extension: '',
        });
      } else {
        setApiError(response.message || 'Failed to initiate call');
      }
    } catch (error) {
      const apiError = error as ApiError;
      setApiError(apiError.message || 'An error occurred while initiating the call');
    } finally {
      setIsLoading(false);
    }
  };

  const resetSuccess = () => {
    setSuccess(false);
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle className="text-green-900">Call Initiated Successfully!</CardTitle>
              <CardDescription>
                Your AI call has been started. The recipient will receive a call shortly.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <p className="text-sm text-green-800">
                  <strong>What happens next:</strong>
                </p>
                <ul className="text-sm text-green-700 mt-2 space-y-1">
                  <li>• The AI will call {formatPhoneNumber(formData.phoneNumber)}</li>
                  <li>• Call results will be available in the dashboard</li>
                  <li>• You'll receive a follow-up email with the summary</li>
                </ul>
              </div>
              
              <div className="flex space-x-3">
                <Button onClick={resetSuccess} className="flex-1">
                  Make Another Call
                </Button>
                <Link to="/dashboard" className="flex-1">
                  <Button variant="outline" className="w-full">
                    View Dashboard
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <Link to="/" className="inline-flex items-center text-primary-600 hover:text-primary-700 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">Initiate AI Call</h1>
          <p className="mt-2 text-gray-600">
            Start an AI-powered conversation with your prospect or customer
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Phone className="h-5 w-5 mr-2 text-primary-600" />
              Call Details
            </CardTitle>
            <CardDescription>
              Provide the contact information for the AI call recipient
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {apiError && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
                  <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Call Failed</p>
                    <p className="text-sm">{apiError}</p>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="First Name *"
                  type="text"
                  value={formData.firstName}
                  onChange={handleInputChange('firstName')}
                  error={errors.firstName}
                  placeholder="John"
                  disabled={isLoading}
                />

                <Input
                  label="Last Name"
                  type="text"
                  value={formData.lastName}
                  onChange={handleInputChange('lastName')}
                  error={errors.lastName}
                  placeholder="Doe"
                  disabled={isLoading}
                />
              </div>

              <Input
                label="Email Address *"
                type="email"
                value={formData.email}
                onChange={handleInputChange('email')}
                error={errors.email}
                placeholder="<EMAIL>"
                helperText="Used for follow-up communications and call summaries"
                disabled={isLoading}
              />

              <Input
                label="Phone Number *"
                type="tel"
                value={formData.phoneNumber}
                onChange={handleInputChange('phoneNumber')}
                error={errors.phoneNumber}
                placeholder="+****************"
                helperText="Include country code for international numbers"
                disabled={isLoading}
              />

              <Input
                label="Extension (Optional)"
                type="text"
                value={formData.extension}
                onChange={handleInputChange('extension')}
                error={errors.extension}
                placeholder="12345"
                helperText="5-digit company extension if applicable"
                disabled={isLoading}
              />

              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h4 className="text-sm font-medium text-blue-900 mb-2">
                  Legal Disclaimer
                </h4>
                <p className="text-xs text-blue-800">
                  By initiating this call, you confirm that you have consent to contact this person 
                  and that the call may be recorded for quality and training purposes. This is a 
                  demonstration of AI calling technology.
                </p>
              </div>

              <Button
                type="submit"
                className="w-full"
                size="lg"
                loading={isLoading}
                disabled={!formData.firstName || !formData.email || !formData.phoneNumber}
              >
                {isLoading ? 'Initiating Call...' : 'Start AI Call'}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default InitiateCall;
