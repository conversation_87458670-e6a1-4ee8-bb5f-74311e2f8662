import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout';
import CallHistoryTable from '../components/CallHistoryTable';
import { Button } from '../components/ui/Button';
import { callApi } from '../services/api';
import { InboundCall, OutboundCall, ApiError } from '../types/api';
import { Plus, RefreshCw, AlertCircle } from 'lucide-react';

type CallRecord = (InboundCall | OutboundCall) & {
  type: 'inbound' | 'outbound';
};

const Calls: React.FC = () => {
  const [calls, setCalls] = useState<CallRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCalls = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch both inbound and outbound calls
      const [inboundCalls, outboundCalls] = await Promise.all([
        callApi.getInboundCalls(),
        callApi.getOutboundCalls(),
      ]);

      // Combine and mark call types
      const allCalls: CallRecord[] = [
        ...inboundCalls.map(call => ({ ...call, type: 'inbound' as const })),
        ...outboundCalls.map(call => ({ ...call, type: 'outbound' as const })),
      ];

      // Sort by timestamp (newest first)
      allCalls.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      setCalls(allCalls);
    } catch (err) {
      const apiError = err as ApiError;
      setError(apiError.message || 'Failed to fetch call history');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCalls();
  }, []);

  const handleRefresh = () => {
    fetchCalls();
  };

  if (error) {
    return (
      <Layout>
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Failed to Load Call History
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Call History</h1>
            <p className="text-gray-600">
              View and manage all your inbound and outbound AI calls
            </p>
          </div>
          <div className="flex space-x-3 mt-4 sm:mt-0">
            <Button onClick={handleRefresh} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Link to="/initiate-call">
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                New Call
              </Button>
            </Link>
          </div>
        </div>

        {/* Call History Table */}
        <CallHistoryTable calls={calls} isLoading={isLoading} />

        {/* Summary Stats */}
        {!isLoading && calls.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="text-2xl font-bold text-gray-900">
                {calls.length}
              </div>
              <div className="text-sm text-gray-600">Total Calls</div>
            </div>
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="text-2xl font-bold text-green-600">
                {calls.filter(call => call.type === 'inbound').length}
              </div>
              <div className="text-sm text-gray-600">Inbound Calls</div>
            </div>
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="text-2xl font-bold text-blue-600">
                {calls.filter(call => call.type === 'outbound').length}
              </div>
              <div className="text-sm text-gray-600">Outbound Calls</div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Calls;
