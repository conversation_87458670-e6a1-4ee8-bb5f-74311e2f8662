import React from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout';
import StatsCard from '../components/StatsCard';
import SentimentChart from '../components/SentimentChart';
import { Button } from '../components/ui/Button';
import { Skeleton } from '../components/ui/Loading';
import { useDashboard } from '../hooks/useDashboard';
import { formatPercentage } from '../utils';
import type { InboundCall } from '../types/api';
import { 
  Phone, 
  PhoneCall, 
  TrendingUp, 
  Users, 
  RefreshCw,
  Plus,
  AlertCircle 
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { data, isLoading, error, refetch } = useDashboard();

  const handleRefresh = async () => {
    await refetch();
  };

  if (error) {
    return (
      <Layout>
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Failed to Load Dashboard
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Monitor your AI calling performance and analytics</p>
          </div>
          <div className="flex space-x-3 mt-4 sm:mt-0">
            <Button onClick={handleRefresh} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Link to="/initiate-call">
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                New Call
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {isLoading ? (
            // Loading skeletons
            Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="bg-white p-6 rounded-lg border border-gray-200">
                <Skeleton lines={3} />
              </div>
            ))
          ) : data ? (
            <>
              <StatsCard
                title="Total Calls"
                value={data.stats.total_calls}
                icon={Phone}
                iconColor="text-blue-600"
                description="All time calls made"
              />
              <StatsCard
                title="Inbound Calls"
                value={data.stats.total_inbound}
                icon={PhoneCall}
                iconColor="text-green-600"
                description="Received calls"
              />
              <StatsCard
                title="Outbound Calls"
                value={data.stats.total_outbound}
                icon={Users}
                iconColor="text-purple-600"
                description="Initiated calls"
              />
              <StatsCard
                title="Success Rate"
                value={formatPercentage(data.stats.success_rate)}
                icon={TrendingUp}
                iconColor="text-emerald-600"
                description="Positive sentiment rate"
              />
            </>
          ) : null}
        </div>

        {/* Charts and Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sentiment Chart */}
          <div>
            {isLoading ? (
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <Skeleton lines={2} className="mb-4" />
                <div className="h-64 bg-gray-100 rounded animate-pulse" />
              </div>
            ) : data ? (
              <SentimentChart data={data.sentiment} />
            ) : null}
          </div>

          {/* Quick Actions */}
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <Link to="/initiate-call">
                <Button className="w-full justify-start" variant="outline">
                  <Phone className="h-4 w-4 mr-3" />
                  Start New AI Call
                </Button>
              </Link>
              <Button className="w-full justify-start" variant="outline" disabled>
                <Users className="h-4 w-4 mr-3" />
                Bulk Import Contacts
                <span className="ml-auto text-xs text-gray-500">Coming Soon</span>
              </Button>
              <Button className="w-full justify-start" variant="outline" disabled>
                <TrendingUp className="h-4 w-4 mr-3" />
                Export Analytics
                <span className="ml-auto text-xs text-gray-500">Coming Soon</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Recent Activity Preview */}
        {!isLoading && data && data.recent_calls.length > 0 && (
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Recent Calls</h3>
                <Link to="/dashboard/calls">
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </Link>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {data.recent_calls.slice(0, 5).map((call, index) => (
                  <div key={index} className="flex items-center justify-between py-2">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {'phone_number' in call ? call.phone_number : (call as InboundCall).caller_number}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(call.timestamp).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        call.sentiment?.toLowerCase().includes('positive') 
                          ? 'bg-green-100 text-green-800'
                          : call.sentiment?.toLowerCase().includes('negative')
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {call.sentiment || 'Unknown'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && data && data.stats.total_calls === 0 && (
          <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
            <Phone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No calls yet
            </h3>
            <p className="text-gray-600 mb-6">
              Get started by making your first AI call
            </p>
            <Link to="/initiate-call">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Make Your First Call
              </Button>
            </Link>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Dashboard;
