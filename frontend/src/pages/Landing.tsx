import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '../components/ui/Button';
import {
  Phone,
  Shield,
  Clock,
  MapPin
} from 'lucide-react';

const Landing: React.FC = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-6">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-teal-600 rounded-lg flex items-center justify-center">
                <Phone className="h-6 w-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-gray-800">CapVox</span>
            </div>
            <div className="flex items-center space-x-6">
              <Link to="/login" className="text-gray-600 hover:text-teal-600 font-medium">
                Home
              </Link>
              <Link to="/initiate-call">
                <Button className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-2 rounded-lg font-medium">
                  Initiate Call
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-teal-50 to-green-50 py-20">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-800 mb-6 leading-tight">
            More Funded Deals. More Closed Clients.
          </h1>
          <h2 className="text-4xl md:text-5xl font-bold text-teal-600 mb-8">
            Guaranteed.
          </h2>
          <div className="w-24 h-1 bg-teal-600 mx-auto mb-8"></div>

          <Link to="/initiate-call">
            <Button className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-4 rounded-lg text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200">
              CLICK HERE TO TRY YOUR DEMO
            </Button>
          </Link>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-4xl font-bold text-teal-600 mb-8">
            You Know Follow-Up Is Critical...
          </h2>
          <p className="text-lg text-gray-600 leading-relaxed">
            ...but your team is maxed out. You spend a fortune on leads, yet too many slip through the
            cracks. Inbound inquiries go unanswered, follow-ups get delayed, and each slow response
            means another lost deal. Every day, opportunities are being wasted simply because your sales
            reps can't keep up with the pace.
          </p>
        </div>
      </section>

      {/* Solution Section */}
      <section className="bg-gradient-to-br from-teal-50 to-green-50 py-20">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-teal-600 mb-8">
            The Better Solution: CapVox – Your AI Sales Rep That Never Sleeps
          </h2>
          <p className="text-lg text-gray-600 leading-relaxed max-w-4xl mx-auto">
            Imagine a sales rep who calls every new lead within seconds, follows up tirelessly 24/7, and never
            asks for a salary or a day off. That's CapVox. It's a done-for-you AI voice agent that acts just like
            your top closer – except it never sleeps. CapVox automatically engages every single lead,
            nurtures them with perfect timing and persistence, and hands off hot, qualified prospects to
            your human team. All without adding to your headcount or workload. (And unlike a DIY software
            tool, we handle everything for you — so you can just focus on funding deals.)
          </p>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <h2 className="text-4xl font-bold text-teal-600 mb-16">
            "OK... But What Makes CapVox Different?"
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            {/* Guarantee */}
            <div className="text-center">
              <div className="w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Shield className="h-10 w-10 text-teal-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">GUARANTEE</h3>
              <p className="text-gray-600 leading-relaxed">
                We only win if you win. You won't be stuck taking all the risk - if CapVox
                doesn't deliver more funded deals, we'll make it right or true
                partnerships built on shared success.
              </p>
            </div>

            {/* 24/7 Follow-up */}
            <div className="text-center">
              <div className="w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Clock className="h-10 w-10 text-teal-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">24/7 FOLLOW-UP</h3>
              <p className="text-gray-600 leading-relaxed">
                CapVox never takes a break. It calls and follows up with your leads, day,
                night, and weekends. Every inquiry gets instant attention, 24/7 AM
                on a holiday. While your competitors sleep, CapVox works.
              </p>
            </div>

            {/* U.S.-Based */}
            <div className="text-center">
              <div className="w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <MapPin className="h-10 w-10 text-teal-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">U.S.-BASED</h3>
              <p className="text-gray-600 leading-relaxed">
                We're not an overseas tech company hiding behind chat support. CapVox is
                built and backed by a U.S.-based team, so you can reach a decision
                when you need help. (And our AI sounds authentically American.)
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-br from-teal-50 to-green-50 py-20">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-4xl font-bold text-gray-800 mb-6">
            Ready to Transform Your Sales Process?
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Join hundreds of businesses already using CapVox to close more deals and grow faster.
          </p>
          <Link to="/initiate-call">
            <Button className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-4 rounded-lg text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200">
              GET STARTED TODAY
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-teal-600 rounded-lg flex items-center justify-center">
                <Phone className="h-6 w-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-white">CapVox</span>
            </div>

            <p className="text-gray-400 mb-8">
              Your AI Sales Rep That Never Sleeps
            </p>

            <div className="flex justify-center space-x-6 mb-8">
              <Link to="/login" className="text-gray-400 hover:text-white transition-colors">
                Home
              </Link>
              <Link to="/initiate-call" className="text-gray-400 hover:text-white transition-colors">
                Initiate Call
              </Link>
            </div>

            <div className="pt-8 border-t border-gray-700 text-gray-400 text-sm">
              © 2024 CapVox. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;
