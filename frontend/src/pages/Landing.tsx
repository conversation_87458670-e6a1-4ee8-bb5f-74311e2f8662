import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '../components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card';
import {
  Phone,
  Clock,
  BarChart3,
  Users,
  Zap,
  Shield,
  ArrowRight,
  CheckCircle,
  Star,
  PlayCircle,
  TrendingUp,
  Globe
} from 'lucide-react';

const Landing: React.FC = () => {
  const features = [
    {
      icon: Phone,
      title: '24/7 AI Calling',
      description: 'Automated AI-powered calls that work around the clock to engage your prospects and customers.',
    },
    {
      icon: BarChart3,
      title: 'Real-time Analytics',
      description: 'Get instant insights into call performance, sentiment analysis, and conversion metrics.',
    },
    {
      icon: Clock,
      title: 'Instant Follow-up',
      description: 'Automated follow-up calls and messages based on conversation outcomes and customer responses.',
    },
    {
      icon: Users,
      title: 'Personalized Conversations',
      description: 'AI adapts to each customer\'s needs and preferences for more meaningful interactions.',
    },
    {
      icon: Zap,
      title: 'Lightning Fast Setup',
      description: 'Get started in minutes with our intuitive interface and pre-built conversation templates.',
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Bank-level security and compliance to protect your customer data and conversations.',
    },
  ];

  const benefits = [
    'Increase conversion rates by up to 300%',
    'Reduce operational costs by 70%',
    'Scale your outreach without hiring more staff',
    'Never miss a lead or follow-up opportunity',
    'Get detailed insights into customer sentiment',
    'Integrate seamlessly with your existing CRM',
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white/95 backdrop-blur-sm shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center">
                <Phone className="h-5 w-5 text-white" />
              </div>
              <div className="text-xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent">
                Conversa AI
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/login">
                <Button variant="ghost" className="text-gray-600 hover:text-primary-600">
                  Sign In
                </Button>
              </Link>
              <Link to="/initiate-call">
                <Button className="btn-primary shadow-lg">
                  <PlayCircle className="mr-2 h-4 w-4" />
                  Try Demo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 py-24 overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-0 left-0 w-72 h-72 bg-primary-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute top-0 right-0 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center animate-fade-in-up">
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-800 text-sm font-medium mb-8">
              <Star className="mr-2 h-4 w-4" />
              #1 AI Calling Platform
            </div>

            <h1 className="text-5xl md:text-7xl font-extrabold text-gray-900 mb-8 leading-tight">
              Transform Your Business with{' '}
              <span className="bg-gradient-to-r from-primary-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                AI-Powered Calling
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
              Automate your customer outreach, follow-ups, and support calls with intelligent AI agents
              that sound human and deliver results <span className="font-semibold text-primary-600">24/7</span>.
            </p>

            {/* Stats */}
            <div className="flex flex-wrap justify-center gap-8 mb-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600">300%</div>
                <div className="text-sm text-gray-600">Conversion Increase</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600">70%</div>
                <div className="text-sm text-gray-600">Cost Reduction</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600">24/7</div>
                <div className="text-sm text-gray-600">Availability</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link to="/initiate-call">
                <Button size="lg" className="btn-primary text-lg px-8 py-4 w-full sm:w-auto shadow-2xl">
                  <PlayCircle className="mr-3 h-6 w-6" />
                  Start Free Demo
                  <ArrowRight className="ml-3 h-6 w-6" />
                </Button>
              </Link>
              <Link to="/login">
                <Button variant="outline" size="lg" className="text-lg px-8 py-4 w-full sm:w-auto border-2 hover:bg-gray-50">
                  <BarChart3 className="mr-3 h-6 w-6" />
                  View Dashboard
                </Button>
              </Link>
            </div>

            <p className="text-sm text-gray-500 mt-6">
              ✨ No credit card required • Setup in 2 minutes • Free 14-day trial
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-white relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-gray-100 text-gray-800 text-sm font-medium mb-6">
              <Zap className="mr-2 h-4 w-4" />
              Powerful Features
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Everything You Need to{' '}
              <span className="text-primary-600">Scale Your Business</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our comprehensive AI calling platform provides all the tools you need to automate,
              optimize, and scale your customer communications.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card
                  key={index}
                  className="group hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 border-0 shadow-lg bg-gradient-to-br from-white to-gray-50"
                >
                  <CardHeader className="pb-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-600 text-base leading-relaxed">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Additional feature highlight */}
          <div className="mt-20 text-center">
            <div className="inline-flex items-center space-x-8 bg-gradient-to-r from-primary-50 to-indigo-50 rounded-2xl p-8">
              <div className="flex items-center space-x-3">
                <TrendingUp className="h-8 w-8 text-primary-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">10,000+</div>
                  <div className="text-sm text-gray-600">Successful Calls Daily</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Globe className="h-8 w-8 text-primary-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">50+</div>
                  <div className="text-sm text-gray-600">Countries Supported</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Users className="h-8 w-8 text-primary-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">1,000+</div>
                  <div className="text-sm text-gray-600">Happy Customers</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-24 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="animate-fade-in-up">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-800 text-sm font-medium mb-6">
                <CheckCircle className="mr-2 h-4 w-4" />
                Proven Results
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
                Why Choose{' '}
                <span className="text-primary-600">Conversa AI?</span>
              </h2>
              <p className="text-xl text-gray-600 mb-10 leading-relaxed">
                Join thousands of businesses that have transformed their customer engagement
                with our AI-powered calling platform and achieved remarkable results.
              </p>
              <div className="space-y-6">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start group">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-4 group-hover:bg-green-200 transition-colors">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                    <span className="text-lg text-gray-700 group-hover:text-gray-900 transition-colors">
                      {benefit}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              {/* Background decoration */}
              <div className="absolute -inset-4 bg-gradient-to-r from-primary-500 to-purple-600 rounded-3xl blur opacity-20"></div>

              <div className="relative bg-white p-10 rounded-3xl shadow-2xl border border-gray-100">
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <PlayCircle className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    Ready to Get Started?
                  </h3>
                  <p className="text-gray-600 text-lg mb-8">
                    Experience the power of AI calling with our free demo.
                    No credit card required, setup in minutes.
                  </p>
                </div>

                <div className="space-y-4">
                  <Link to="/initiate-call">
                    <Button size="lg" className="w-full btn-primary text-lg py-4">
                      <PlayCircle className="mr-3 h-6 w-6" />
                      Try Free Demo Now
                      <ArrowRight className="ml-3 h-6 w-6" />
                    </Button>
                  </Link>

                  <Link to="/login">
                    <Button variant="outline" size="lg" className="w-full text-lg py-4 border-2">
                      <BarChart3 className="mr-3 h-6 w-6" />
                      View Dashboard
                    </Button>
                  </Link>
                </div>

                <div className="mt-8 pt-6 border-t border-gray-100">
                  <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
                    <div className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      Free 14-day trial
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      No setup fees
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      Cancel anytime
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* Logo and brand */}
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center">
                <Phone className="h-6 w-6 text-white" />
              </div>
              <div className="text-3xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                Conversa AI
              </div>
            </div>

            <p className="text-xl text-gray-300 mb-10 max-w-2xl mx-auto">
              Revolutionizing business communication with AI-powered calling solutions
              that deliver real results for modern enterprises.
            </p>

            {/* Navigation links */}
            <div className="flex flex-wrap justify-center gap-8 mb-12">
              <Link
                to="/login"
                className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2"
              >
                <BarChart3 className="h-5 w-5" />
                <span>Dashboard</span>
              </Link>
              <Link
                to="/initiate-call"
                className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2"
              >
                <PlayCircle className="h-5 w-5" />
                <span>Demo</span>
              </Link>
              <a
                href="#features"
                className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2"
              >
                <Zap className="h-5 w-5" />
                <span>Features</span>
              </a>
            </div>

            {/* Social proof */}
            <div className="flex justify-center items-center space-x-8 mb-10">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-400">99.9%</div>
                <div className="text-sm text-gray-400">Uptime</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-400">24/7</div>
                <div className="text-sm text-gray-400">Support</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-400">SOC2</div>
                <div className="text-sm text-gray-400">Compliant</div>
              </div>
            </div>

            {/* Copyright */}
            <div className="pt-8 border-t border-gray-700">
              <p className="text-gray-400 text-sm">
                © 2024 Conversa AI. All rights reserved. Built with ❤️ for modern businesses.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;
