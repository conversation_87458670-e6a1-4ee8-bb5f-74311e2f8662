import axios, { AxiosResponse } from 'axios';
import {
  LoginRequest,
  LoginResponse,
  CallRequest,
  CallResponse,
  InboundCall,
  OutboundCall,
  DashboardData,
  SentimentData,
  CallStats,
  ApiError
} from '../types/api';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout for calls
});

// Request interceptor for adding auth tokens if needed
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    const apiError: ApiError = {
      message: error.response?.data?.message || error.message || 'An error occurred',
      status: error.response?.status,
      details: error.response?.data?.details
    };
    return Promise.reject(apiError);
  }
);

// Authentication API
export const authApi = {
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response: AxiosResponse<LoginResponse> = await api.post('/api/login', credentials);
    return response.data;
  },
};

// Call Management API
export const callApi = {
  initiateCall: async (callData: CallRequest): Promise<CallResponse> => {
    const response: AxiosResponse<CallResponse> = await api.post('/api/outgoing', callData);
    return response.data;
  },

  getInboundCalls: async (): Promise<InboundCall[]> => {
    const response: AxiosResponse<InboundCall[]> = await api.get('/api/inbound-calls-dashboard');
    return response.data;
  },

  getOutboundCalls: async (): Promise<OutboundCall[]> => {
    const response: AxiosResponse<OutboundCall[]> = await api.get('/api/outbound-calls-dashboard');
    return response.data;
  },
};

// Dashboard API
export const dashboardApi = {
  getDashboardData: async (): Promise<DashboardData> => {
    // Since the backend doesn't have a combined dashboard endpoint,
    // we'll fetch data from multiple endpoints and combine them
    const [inboundCalls, outboundCalls] = await Promise.all([
      callApi.getInboundCalls(),
      callApi.getOutboundCalls()
    ]);

    // Calculate sentiment data
    const allCalls = [...inboundCalls, ...outboundCalls];
    const sentimentCounts = allCalls.reduce(
      (acc, call) => {
        const sentiment = call.sentiment?.toLowerCase() || 'unknown';
        if (sentiment.includes('positive')) acc.positive++;
        else if (sentiment.includes('negative')) acc.negative++;
        else acc.neutral++;
        return acc;
      },
      { positive: 0, neutral: 0, negative: 0 }
    );

    // Calculate stats
    const stats: CallStats = {
      total_calls: allCalls.length,
      total_inbound: inboundCalls.length,
      total_outbound: outboundCalls.length,
      success_rate: allCalls.length > 0 ? (sentimentCounts.positive / allCalls.length) * 100 : 0,
      average_duration: 0, // Backend doesn't provide duration data
    };

    // Get recent calls (last 10)
    const recent_calls = allCalls
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10);

    return {
      sentiment: sentimentCounts,
      stats,
      recent_calls
    };
  },

  getSentimentData: async (): Promise<SentimentData> => {
    const dashboardData = await dashboardApi.getDashboardData();
    return dashboardData.sentiment;
  },

  getCallStats: async (): Promise<CallStats> => {
    const dashboardData = await dashboardApi.getDashboardData();
    return dashboardData.stats;
  },
};

export default api;
