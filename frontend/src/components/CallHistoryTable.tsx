import { useState, useMemo } from 'react';
import type { InboundCall, OutboundCall } from '../types/api';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { formatPhoneNumber, formatDate, getSentimentBadgeColor } from '../utils';
import {
  ChevronUp,
  ChevronDown,
  Search,
  Phone,
  PhoneCall,
  Calendar,
  Clock
} from 'lucide-react';

type CallRecord = (InboundCall | OutboundCall) & {
  type: 'inbound' | 'outbound';
};

interface CallHistoryTableProps {
  calls: CallRecord[];
  isLoading?: boolean;
}

type SortField = 'timestamp' | 'phone_number' | 'sentiment' | 'duration';
type SortDirection = 'asc' | 'desc';

const CallHistoryTable: React.FC<CallHistoryTableProps> = ({ calls, isLoading = false }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<SortField>('timestamp');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [filterType, setFilterType] = useState<'all' | 'inbound' | 'outbound'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Filter and search calls
  const filteredCalls = useMemo(() => {
    return calls.filter(call => {
      // Type filter
      if (filterType !== 'all' && call.type !== filterType) {
        return false;
      }

      // Search filter
      if (searchTerm) {
        const phoneNumber = call.type === 'outbound' ? (call as OutboundCall).phone_number : (call as InboundCall).caller_number;
        const searchLower = searchTerm.toLowerCase();
        return (
          (phoneNumber && phoneNumber.includes(searchLower)) ||
          (call.sentiment && call.sentiment.toLowerCase().includes(searchLower)) ||
          ('first_name' in call && call.first_name?.toLowerCase().includes(searchLower)) ||
          ('last_name' in call && call.last_name?.toLowerCase().includes(searchLower))
        );
      }

      return true;
    });
  }, [calls, searchTerm, filterType]);

  // Sort calls
  const sortedCalls = useMemo(() => {
    return [...filteredCalls].sort((a, b) => {
      let aValue: string | number | Date;
      let bValue: string | number | Date;

      switch (sortField) {
        case 'timestamp':
          aValue = new Date(a.timestamp);
          bValue = new Date(b.timestamp);
          break;
        case 'phone_number':
          aValue = a.type === 'outbound' ? (a as OutboundCall).phone_number || '' : (a as InboundCall).caller_number || '';
          bValue = b.type === 'outbound' ? (b as OutboundCall).phone_number || '' : (b as InboundCall).caller_number || '';
          break;
        case 'sentiment':
          aValue = a.sentiment || '';
          bValue = b.sentiment || '';
          break;
        case 'duration':
          aValue = a.duration || 0;
          bValue = b.duration || 0;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [filteredCalls, sortField, sortDirection]);

  // Paginate calls
  const paginatedCalls = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedCalls.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedCalls, currentPage]);

  const totalPages = Math.ceil(sortedCalls.length / itemsPerPage);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? 
      <ChevronUp className="h-4 w-4" /> : 
      <ChevronDown className="h-4 w-4" />;
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      {/* Header and Filters */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h3 className="text-lg font-semibold text-gray-900">Call History</h3>
          
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search calls..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-full sm:w-64"
              />
            </div>
            
            {/* Filter */}
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as 'all' | 'inbound' | 'outbound')}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Calls</option>
              <option value="inbound">Inbound Only</option>
              <option value="outbound">Outbound Only</option>
            </select>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('phone_number')}
              >
                <div className="flex items-center space-x-1">
                  <span>Phone Number</span>
                  <SortIcon field="phone_number" />
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('sentiment')}
              >
                <div className="flex items-center space-x-1">
                  <span>Sentiment</span>
                  <SortIcon field="sentiment" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('duration')}
              >
                <div className="flex items-center space-x-1">
                  <span>Duration</span>
                  <SortIcon field="duration" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('timestamp')}
              >
                <div className="flex items-center space-x-1">
                  <span>Date</span>
                  <SortIcon field="timestamp" />
                </div>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedCalls.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                  <Phone className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No calls found</p>
                  {searchTerm && (
                    <p className="text-sm">Try adjusting your search or filters</p>
                  )}
                </td>
              </tr>
            ) : (
              paginatedCalls.map((call, index) => {
                const phoneNumber = call.type === 'outbound' ? (call as OutboundCall).phone_number : (call as InboundCall).caller_number;
                const contactName = 'first_name' in call 
                  ? `${call.first_name || ''} ${call.last_name || ''}`.trim()
                  : 'Unknown';

                return (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {call.type === 'inbound' ? (
                          <PhoneCall className="h-4 w-4 text-green-600 mr-2" />
                        ) : (
                          <Phone className="h-4 w-4 text-blue-600 mr-2" />
                        )}
                        <span className="text-sm font-medium text-gray-900 capitalize">
                          {call.type}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {phoneNumber ? formatPhoneNumber(phoneNumber) : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {contactName || 'Unknown'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {call.sentiment ? (
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getSentimentBadgeColor(call.sentiment)}`}>
                          {call.sentiment}
                        </span>
                      ) : (
                        <span className="text-sm text-gray-400">Unknown</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-gray-400 mr-1" />
                        {call.duration ? `${call.duration}s` : 'N/A'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                        {formatDate(call.timestamp)}
                      </div>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, sortedCalls.length)} of {sortedCalls.length} results
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CallHistoryTable;
