import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveC<PERSON>r, <PERSON>, Tooltip } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card';
import { SentimentData } from '../types/api';

interface SentimentChartProps {
  data: SentimentData;
}

const SentimentChart: React.FC<SentimentChartProps> = ({ data }) => {
  const chartData = [
    { name: 'Positive', value: data.positive, color: '#10b981' },
    { name: 'Neutral', value: data.neutral, color: '#f59e0b' },
    { name: 'Negative', value: data.negative, color: '#ef4444' },
  ].filter(item => item.value > 0); // Only show segments with data

  const total = data.positive + data.neutral + data.negative;

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const percentage = total > 0 ? ((data.value / total) * 100).toFixed(1) : '0';
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm text-gray-600">
            {data.value} calls ({percentage}%)
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomLegend = ({ payload }: any) => {
    return (
      <div className="flex justify-center space-x-6 mt-4">
        {payload.map((entry: any, index: number) => {
          const percentage = total > 0 ? ((entry.payload.value / total) * 100).toFixed(1) : '0';
          return (
            <div key={index} className="flex items-center">
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm text-gray-600">
                {entry.value} ({percentage}%)
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  if (total === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Call Sentiment Analysis</CardTitle>
          <CardDescription>
            Breakdown of customer sentiment from recent calls
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <p className="text-lg font-medium">No call data available</p>
              <p className="text-sm">Make some calls to see sentiment analysis</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Call Sentiment Analysis</CardTitle>
        <CardDescription>
          Breakdown of customer sentiment from {total} recent calls
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={2}
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend content={<CustomLegend />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        {/* Summary stats */}
        <div className="grid grid-cols-3 gap-4 mt-6 pt-6 border-t border-gray-200">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{data.positive}</div>
            <div className="text-sm text-gray-500">Positive</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{data.neutral}</div>
            <div className="text-sm text-gray-500">Neutral</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{data.negative}</div>
            <div className="text-sm text-gray-500">Negative</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SentimentChart;
