import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import type { SentimentType } from '../types/api';

// Utility function for merging Tailwind classes
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Format phone number for display
export function formatPhoneNumber(phone: string): string {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Format as (XXX) XXX-XXXX for US numbers
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }
  
  // Format as +X (XXX) XXX-XXXX for international numbers
  if (cleaned.length === 11 && cleaned.startsWith('1')) {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  
  // Return original if doesn't match expected patterns
  return phone;
}

// Format date for display
export function formatDate(dateString: string): string {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
}

// Format relative time (e.g., "2 hours ago")
export function formatRelativeTime(dateString: string): string {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  
  return formatDate(dateString);
}

// Get sentiment color for UI
export function getSentimentColor(sentiment?: string): string {
  if (!sentiment) return 'text-gray-500';
  
  const sentimentLower = sentiment.toLowerCase();
  if (sentimentLower.includes('positive')) return 'text-green-600';
  if (sentimentLower.includes('negative')) return 'text-red-600';
  return 'text-yellow-600';
}

// Get sentiment badge color
export function getSentimentBadgeColor(sentiment?: string): string {
  if (!sentiment) return 'bg-gray-100 text-gray-800';
  
  const sentimentLower = sentiment.toLowerCase();
  if (sentimentLower.includes('positive')) return 'bg-green-100 text-green-800';
  if (sentimentLower.includes('negative')) return 'bg-red-100 text-red-800';
  return 'bg-yellow-100 text-yellow-800';
}

// Normalize sentiment for consistency
export function normalizeSentiment(sentiment?: string): SentimentType {
  if (!sentiment) return 'unknown';
  
  const sentimentLower = sentiment.toLowerCase();
  if (sentimentLower.includes('positive')) return 'positive';
  if (sentimentLower.includes('negative')) return 'negative';
  if (sentimentLower.includes('neutral')) return 'neutral';
  return 'unknown';
}

// Validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate phone number format
export function isValidPhoneNumber(phone: string): boolean {
  const cleaned = phone.replace(/\D/g, '');
  return cleaned.length >= 10 && cleaned.length <= 15;
}

// Format percentage
export function formatPercentage(value: number): string {
  return `${Math.round(value)}%`;
}

// Truncate text with ellipsis
export function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

// Debounce function for search inputs
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Generate random ID for temporary use
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}
