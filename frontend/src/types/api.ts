// API Response Types based on backend models

export interface User {
  id: number;
  username: string;
}

export interface Company {
  id: number;
  extension: string;
  company_name: string;
  website?: string;
  company_linkedin_url?: string;
  facebook_url?: string;
  twitter_url?: string;
  seo_description?: string;
  industry?: string;
  company_phone?: string;
  annual_revenue?: number;
  city?: string;
  state?: string;
  country?: string;
  technologies?: string;
  contact_name?: string;
  email?: string;
}

export interface InboundCall {
  id: number;
  company_id: number;
  timestamp: string;
  caller_number: string;
  sentiment?: string;
  key_comments?: string;
  transcription?: string;
}

export interface OutboundCall {
  id: number;
  company_id: number;
  timestamp: string;
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  email?: string;
  sentiment?: string;
  call_summary?: string;
  next_actions?: string;
  transcription?: string;
}

// API Request Types
export interface CallRequest {
  phone_number: string;
  first_name: string;
  user_email: string;
  extension?: string;
  last_name?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

// API Response Types
export interface LoginResponse {
  authenticated: boolean;
  username?: string;
  message?: string;
}

export interface CallResponse {
  success: boolean;
  message: string;
  call_id?: string;
}

// Dashboard Analytics Types
export interface SentimentData {
  positive: number;
  neutral: number;
  negative: number;
}

export interface CallStats {
  total_calls: number;
  success_rate: number;
  average_duration: number;
  total_inbound: number;
  total_outbound: number;
}

export interface DashboardData {
  sentiment: SentimentData;
  stats: CallStats;
  recent_calls: (InboundCall | OutboundCall)[];
}

// Form Types
export interface CallFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  extension?: string;
}

// Utility Types
export type CallType = 'inbound' | 'outbound';
export type SentimentType = 'positive' | 'neutral' | 'negative' | 'unknown';

export interface ApiError {
  message: string;
  status?: number;
  details?: string;
}
