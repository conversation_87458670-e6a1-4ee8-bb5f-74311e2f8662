import { useState, useEffect } from 'react';
import { DashboardData, ApiError } from '../types/api';
import { dashboardApi } from '../services/api';

interface UseDashboardReturn {
  data: DashboardData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useDashboard = (): UseDashboardReturn => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const dashboardData = await dashboardApi.getDashboardData();
      setData(dashboardData);
    } catch (err) {
      const apiError = err as ApiError;
      setError(apiError.message || 'Failed to fetch dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const refetch = async () => {
    await fetchData();
  };

  return {
    data,
    isLoading,
    error,
    refetch,
  };
};
