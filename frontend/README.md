# Conversa AI Frontend

A modern React frontend application for the Conversa AI calling agent platform. Built with React 18+, TypeScript, Vite, Tailwind CSS, and ShadCN/UI components.

## 🚀 Features

- **Modern Tech Stack**: React 18+ with TypeScript, Vite for fast development
- **Beautiful UI**: Tailwind CSS with custom ShadCN/UI components
- **Authentication**: Token-based authentication with protected routes
- **Dashboard Analytics**: Real-time call statistics and sentiment analysis
- **Call Management**: Initiate AI calls and view call history
- **Responsive Design**: Mobile-first responsive design
- **Error Handling**: Comprehensive error boundaries and loading states
- **Accessibility**: ARIA labels and keyboard navigation support

## 📋 Prerequisites

- Node.js 18+
- npm or yarn
- Backend API running on `http://localhost:8000` (configurable via environment variables)

## 🛠️ Installation

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```

   Update `.env` with your configuration:
   ```env
   VITE_API_BASE_URL=http://localhost:8000
   VITE_APP_NAME=Conversa AI
   ```

4. **Start the development server**:
   ```bash
   npm run dev
   ```

5. **Open your browser** and navigate to `http://localhost:5173`

## 🏗️ Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── ui/           # Base UI components (Button, Input, Card, etc.)
│   │   ├── Layout.tsx    # Main layout with navigation
│   │   ├── ProtectedRoute.tsx
│   │   ├── ErrorBoundary.tsx
│   │   └── ...
│   ├── pages/            # Page components
│   │   ├── Landing.tsx   # Landing page
│   │   ├── Login.tsx     # Authentication page
│   │   ├── Dashboard.tsx # Main dashboard
│   │   ├── InitiateCall.tsx
│   │   ├── Calls.tsx     # Call history
│   │   └── NotFound.tsx
│   ├── contexts/         # React contexts
│   │   └── AuthContext.tsx
│   ├── hooks/            # Custom React hooks
│   │   └── useDashboard.ts
│   ├── services/         # API service functions
│   │   └── api.ts
│   ├── types/            # TypeScript type definitions
│   │   └── api.ts
│   ├── utils/            # Utility functions
│   │   └── index.ts
│   ├── App.tsx           # Main app component
│   ├── main.tsx          # App entry point
│   └── index.css         # Global styles
├── package.json
├── tailwind.config.js    # Tailwind configuration
├── tsconfig.json         # TypeScript configuration
├── vite.config.ts        # Vite configuration
└── README.md
```

## 🎯 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `VITE_API_BASE_URL` | Backend API base URL | `http://localhost:8000` |
| `VITE_APP_NAME` | Application name | `Conversa AI` |

### Tailwind CSS

The project uses a custom Tailwind configuration with:
- Custom color palette (primary/secondary colors)
- Extended animations and keyframes
- Custom component classes for buttons, cards, and inputs

### API Integration

The frontend integrates with the FastAPI backend through:
- Authentication endpoints (`/api/login`)
- Call management (`/api/outgoing`, `/api/inbound`)
- Dashboard data (`/api/dashboard`)

## 📱 Pages and Features

### Landing Page (`/`)
- Hero section with value proposition
- Features showcase
- Call-to-action buttons
- Responsive design

### Authentication (`/login`)
- Simple username/password login
- Token-based authentication
- Automatic redirect after login
- Demo credentials support

### Dashboard (`/dashboard`)
- Real-time call statistics
- Sentiment analysis charts
- Quick action buttons
- Recent calls preview

### Call Initiation (`/initiate-call`)
- Form validation for contact details
- Phone number formatting
- Integration with backend API
- Success/error handling

### Call History (`/dashboard/calls`)
- Sortable and filterable table
- Pagination support
- Search functionality
- Call type filtering (inbound/outbound)

## 🎨 UI Components

### Base Components
- **Button**: Multiple variants (default, outline, ghost, etc.)
- **Input**: With labels, validation, and error states
- **Card**: Flexible card component with header/content/footer
- **Loading**: Spinner and skeleton loading states

### Layout Components
- **Layout**: Main layout with navigation and user menu
- **ProtectedRoute**: Route protection for authenticated users
- **ErrorBoundary**: Error handling and recovery

## 🔒 Authentication

The app uses a simple token-based authentication system:
1. User logs in with username/password
2. Backend returns a token
3. Token is stored in localStorage
4. Token is included in API requests via Axios interceptors
5. Protected routes redirect to login if not authenticated

## 📊 State Management

- **React Context**: Used for authentication state
- **Custom Hooks**: For data fetching and state management
- **Local State**: Component-level state with useState/useEffect

## 🚀 Deployment

### Production Build

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Preview the build**:
   ```bash
   npm run preview
   ```

3. **Deploy the `dist` folder** to your hosting provider

### Environment Setup

For production deployment:
1. Update `VITE_API_BASE_URL` to your production API URL
2. Ensure CORS is configured on your backend
3. Configure your web server to serve the SPA correctly

## 🐛 Troubleshooting

### Common Issues

1. **API Connection Errors**:
   - Check if backend is running
   - Verify `VITE_API_BASE_URL` in `.env`
   - Check CORS configuration

2. **Build Errors**:
   - Run `npm run type-check` to check TypeScript errors
   - Ensure all dependencies are installed

3. **Styling Issues**:
   - Check if Tailwind CSS is properly configured
   - Verify custom CSS classes in `index.css`

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Use TypeScript for type safety
3. Add proper error handling and loading states
4. Test your changes thoroughly
5. Update documentation as needed

## 📄 License

This project is part of the Conversa AI platform. All rights reserved.
