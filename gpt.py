import requests
import os
import json
from typing import Dict, Any, Optional, Literal
from dotenv import load_dotenv
from openai import OpenAI

load_dotenv()

# Get API key from environment variable
PERPLEXITY_API_KEY = os.getenv("PERPLEXITY_API_KEY")

if not PERPLEXITY_API_KEY:
    raise ValueError("PERPLEXITY_API_KEY not found in environment variables")

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

class CompanyResearchProcessor:
    def __init__(self):
        self.perplexity_url = "https://api.perplexity.ai/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {PERPLEXITY_API_KEY}",
            "Content-Type": "application/json"
        }
    
    def search_company_info(self, company_name: str, website: str = None, linkedin_url: str = None) -> Optional[Dict[str, Any]]:
        """
        Search for information about a company using Perplexity AI
        """
        print(f"Searching for information about {company_name}...")
        
        # Build search query
        search_query = f"Research the company {company_name}."
        if website:
            search_query += f" The company website is {website}."
        if linkedin_url:
            search_query += f" The company LinkedIn page is {linkedin_url}."
        
        search_query += """ Please provide a comprehensive analysis of the company including:
1) Detailed company description and mission statement
2) Complete list of products and services with descriptions
3) Target market segments and customer profiles
4) Company size, revenue, and growth metrics
5) Company history, founding date, and major milestones
6) Leadership team and key personnel
7) Industry position and competitive advantages
8) Recent news, achievements, or awards
9) Company culture and values
10) Geographic presence and office locations
11) Technology stack and infrastructure
12) Partnerships and strategic alliances
13) Customer testimonials or case studies
14) Future plans and growth strategy

Format the response as a structured JSON with the following fields:
{
    "companyDescription": "Detailed company overview and mission",
    "mainProducts": "Comprehensive list of products/services with descriptions",
    "targetMarket": "Detailed market segments and customer profiles",
    "companySize": "Size, revenue, employee count, and growth metrics",
    "companyHistory": "Founding date, milestones, and evolution",
    "leadership": "Key executives and leadership team",
    "industryPosition": "Market position and competitive advantages",
    "recentNews": "Recent achievements, awards, or significant events",
    "companyCulture": "Values, culture, and work environment",
    "locations": "Geographic presence and office locations",
    "technologies": "Technology stack and infrastructure details",
    "partnerships": "Strategic alliances and partnerships",
    "testimonials": "Customer success stories and testimonials",
    "futurePlans": "Growth strategy and future initiatives",
    "additionalDetails": "Any other relevant information"
}"""
        
        # Build request payload
        payload = {
            "model": "sonar",  # Using Perplexity's Sonar model for web search capabilities
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful AI research assistant that provides accurate information about companies. Format your response as valid JSON."
                },
                {
                    "role": "user",
                    "content": search_query
                }
            ]
        }
        
        try:
            # Make API request
            response = requests.post(self.perplexity_url, json=payload, headers=self.headers)
            response.raise_for_status()
            result = response.json()
            
            # Extract the content from Perplexity's response
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                #print("Received response from Perplexity AI")
                
                # Try to extract JSON from the response (it might be embedded in markdown code blocks)
                try:
                    # First try to parse as pure JSON
                    company_data = json.loads(content)
                    return company_data
                except json.JSONDecodeError:
                    # Try to extract JSON from markdown code blocks
                    if "```json" in content and "```" in content:
                        json_content = content.split("```json")[1].split("```")[0].strip()
                        try:
                            company_data = json.loads(json_content)
                            return company_data
                        except json.JSONDecodeError:
                            print("Failed to parse JSON from code block")
                    
                    print("Could not parse JSON from Perplexity response, returning raw text")
                    return {"rawDescription": content}
            else:
                print("No valid content in Perplexity response")
                return None
                
        except Exception as e:
            print(f"Error searching for company information: {str(e)}")
            return None
    
    def analyze_call_sentiment(self, call_summary: str) -> Literal["Positive", "Negative", "Neutral"]:
        """
        Analyze the sentiment of a call summary and return only Positive, Negative, or Neutral
        """
        print("Analyzing call sentiment...")

        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "Classify the sentiment of the user's input as exactly one of: Positive, Negative, or Neutral. Respond with ONLY one of these words and nothing else."
                    },
                    {
                        "role": "user",
                        "content": f"Call summary: {call_summary}"
                    }
                ],
                temperature=0.1
            )

            sentiment = response.choices[0].message.content.strip().capitalize()

            if sentiment in {"Positive", "Negative", "Neutral"}:
                return sentiment
            else:
                print(f"Unexpected response: {sentiment}")
                return "Neutral"

        except Exception as e:
            print(f"Error analyzing sentiment: {e}")
            return "Neutral"
    
    def generate_ultravox_prompt(self, company_db_data: Dict[str, Any], web_research: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a prompt for Ultravox AI based on company DB data and web research
        """
        company_name = company_db_data.get("company_name", "the company")
        industry = company_db_data.get("industry", "")
        technologies = company_db_data.get("technologies", "")
        seo_description = company_db_data.get("seo_description", "")
        
        # Start with basic company info
        prompt = f"""
You are an AI virtual assistant representing {company_name}.
"""
        
        # Add industry information if available
        if industry:
            prompt += f"You are a company in the {industry} industry. "
        
        # Add SEO description if available
        if seo_description:
            prompt += f"\n\nCompany description: {seo_description}\n"
        
        # Add technologies if available
        if technologies:
            prompt += f"\nThe company uses technologies including: {technologies}.\n"
        
        # Add web research information if available
        if web_research:
            if "companyDescription" in web_research:
                prompt += f"\nDetailed company overview: {web_research.get('companyDescription')}\n"
            
            if "mainProducts" in web_research:
                prompt += f"\nProducts and services: {web_research.get('mainProducts')}\n"
                
            if "targetMarket" in web_research:
                prompt += f"\nTarget market: {web_research.get('targetMarket')}\n"
            
            if "companySize" in web_research:
                prompt += f"\nCompany size and metrics: {web_research.get('companySize')}\n"
            
            if "companyHistory" in web_research:
                prompt += f"\nCompany history: {web_research.get('companyHistory')}\n"
            
            if "leadership" in web_research:
                prompt += f"\nLeadership team: {web_research.get('leadership')}\n"
            
            if "industryPosition" in web_research:
                prompt += f"\nIndustry position: {web_research.get('industryPosition')}\n"
            
            if "recentNews" in web_research:
                prompt += f"\nRecent news and achievements: {web_research.get('recentNews')}\n"
            
            if "companyCulture" in web_research:
                prompt += f"\nCompany culture: {web_research.get('companyCulture')}\n"
            
            if "locations" in web_research:
                prompt += f"\nGeographic presence: {web_research.get('locations')}\n"
            
            if "technologies" in web_research:
                prompt += f"\nTechnology infrastructure: {web_research.get('technologies')}\n"
            
            if "partnerships" in web_research:
                prompt += f"\nStrategic partnerships: {web_research.get('partnerships')}\n"
            
            if "testimonials" in web_research:
                prompt += f"\nCustomer success stories: {web_research.get('testimonials')}\n"
            
            if "futurePlans" in web_research:
                prompt += f"\nFuture plans: {web_research.get('futurePlans')}\n"
            
            if "additionalDetails" in web_research:
                prompt += f"\nAdditional information: {web_research.get('additionalDetails')}\n"
        
        # Add instructions for AI behavior
        prompt += """
You are answering an incoming phone call from a customer.
Welcome to CapVox AI. This is a demonstration call for internal evaluation purposes only. Thank you for testing your personalized demo.
Your communication style should be warm, natural, and conversational - like a friendly human colleague.
Avoid sounding robotic or reading from a script. Instead, engage in a natural back-and-forth dialogue.
Keep your responses concise but personable. Use casual, everyday language while maintaining professionalism.
Be proactive in asking relevant questions to understand the caller's needs better.
Show empathy and understanding in your responses.

Remember, our tone is conversational, slightly assertive, friendly, and confident. We always end with a follow-up or clarifying question. Ensure a natural pause after each question to allow for the caller's response.

Fallback Example (if stuck or unsure):

"That's a good question. Based on what I've seen, most clients in your space are solving that by speeding up follow-up. Are you currently using reps or software to manage that?"

When discussing the company:
- Use specific details about products, services, and achievements
- Reference recent news or developments when relevant
- Share relevant customer success stories
- Mention specific industry expertise and experience
- Highlight unique selling points and competitive advantages
- Discuss future plans and growth opportunities when appropriate

If you don't know something specific, be honest and offer to find out or connect them with someone who can help.
Your goal is to provide helpful information while making the caller feel comfortable and valued.
"""
        
        return prompt
    
    def process_company(self, company_data: Dict[str, Any]) -> str:
        """
        Process company data from database and generate an Ultravox prompt
        """
        # Extract company information
        company_name = company_data.get("company_name", "")
        website = company_data.get("website", "")
        linkedin_url = company_data.get("company_linkedin_url", "")
        
        # Skip web research if no company name is provided
        if not company_name:
            print("No company name provided, skipping web research")
            return self.generate_ultravox_prompt(company_data)
        
        # Perform web research
        web_research = self.search_company_info(company_name, website, linkedin_url)
        
        # Generate prompt
        prompt = self.generate_ultravox_prompt(company_data, web_research)
        
        return prompt

    def generate_next_actions(self, call_summary: str) -> str:
        """
        Generate concise next action recommendations based on call summary
        Returns a string with 1-2 lines of concrete next steps
        """
        print("Generating next actions from call summary...")

        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert business consultant. Your job is to analyze a call summary and recommend 1-2 specific, actionable next steps for company and call feedback. Be extremely concise and direct. Limit your response to 1-2 short sentences. Focus on concrete, actionable comments only."
                    },
                    {
                        "role": "user",
                        "content": f"Based on this call summary, suggest 1-2 specific next actions the company should take for improvement. Be extremely concise. Return plain text, no formatting, no asterisks, just plain text. Summary: {call_summary}"
                    }
                ],
                temperature=0.3,
                max_tokens=200
            )

            next_actions = response.choices[0].message.content.strip()

            if next_actions:
                return next_actions
            else:
                print("No valid content in response")
                return "Follow up with customer to address concerns."

        except Exception as e:
            print(f"Error generating next actions: {e}")
            return "Schedule follow-up call to address customer needs."
            
    def generate_email_content_outbound(self, first_name: str = None, last_name: str = None, transcript: str = None) -> str:
        """
        Generate a personalized email based on call transcript
        Returns a short, casual email (3–5 sentences) in Gary Halbert style,
        reminding the prospect of what they said, teasing benefits, and
        encouraging them to book a Zoom call.

        Args:
            first_name (str): Recipient's first name
            last_name (str): Recipient's last name
            transcript (str): Call transcript to extract key points

        Returns:
            str: Personalized email content + letter head
        """
        print("Generating personalized email content for outbound call...")

        # Create name for personalization
        name = first_name or last_name or ""

        try:
            response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": (
                        "You are writing on behalf of CapVox, an AI follow-up automation "
                        "platform built for MCA companies.\n\n"
                        "Your job is to analyze the transcript from a call the prospect "
                        "just completed with an AI demo. From this transcript, extract:\n"
                        "- The prospect's name and company (if mentioned)\n"
                        "- What they said about lead volume, follow-up problems, current tech stack, or team size\n"
                        "- Any frustration, skepticism, or strong feelings they shared\n"
                        "- Their tone, objections, and urgency level\n\n"
                        "Now, using the Gary Halbert copywriting style, write the following:\n"
                        "1. A short, casual, personalized email (3–5 sentences) that reminds "
                        "them of what they said, teases the benefits, and encourages them to "
                        "book a Zoom call. Make it feel like it was written by a smart, "
                        "persuasive friend — not a corporate rep.\n\n"
                        "Write both pieces using their language, tone, and phrasing where possible. "
                        "You are not allowed to sound generic or templated.\n\n"
                        "IMPORTANT:\n"
                        "- Your reply will be forwarded exactly as-is. Do NOT include any notes, comments, or explanation.\n"
                        "- This is direct communication with the lead — your output must sound natural and one-to-one.\n"
                        "- Do not make up information — only refer to what is clearly stated in the transcript.\n"
                        "- If details are missing, leave them out. Never guess or fill in gaps.\n"
                        "- Do not include subject lines or internal instructions.\n"
                        "- Include this booking link at the end: https://calendly.com/dromel/30min"
                        "- Close the email with: Warm regards, CapVox AI"
                    )
                },
                {
                    "role": "user",
                    "content": f"Transcript for {name}: {transcript or 'A discussion about how our AI calling system can help improve lead conversion rates.'}"
                }
            ],
            temperature=0.7,
            max_tokens=300
            )

            email_content = response.choices[0].message.content.strip()

            if email_content:
                return email_content

        except Exception as e:
            print(f"Error generating email content: {e}")
            return None        
                
    def generate_sms_content(self, transcript: str = None) -> str:
        """
        Generate a punchy SMS message under 155 characters with a calendar link
        
        Args:
            transcript (str): Call transcript to extract key points
            
        Returns:
            str: SMS content under 155 characters
        """
        print("Generating SMS content...")
        
        try:
            response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": (
                        "You are writing on behalf of CapVox, an AI follow-up automation "
                        "platform built for MCA companies.\n\n"
                        "Your job is to analyze the transcript from a call the prospect "
                        "just completed with an AI demo. From this transcript, extract:\n"
                        "- The prospect's name and company (if mentioned)\n"
                        "- What they said about lead volume, follow-up problems, current tech stack, or team size\n"
                        "- Any frustration, skepticism, or strong feelings they shared\n"
                        "- Their tone, objections, and urgency level\n\n"
                        "Now, using the Gary Halbert copywriting style, write the following:\n"
                        "- A text message under 155 characters with a punchy tone, referencing the same context and linking them to their custom calendar booking link.\n"
                        "- It's just a casual, concise SMS message — no closing or signature is required.\n\n"
                        "Write using their language, tone, and phrasing where possible. "
                        "You are not allowed to sound generic or templated.\n\n"
                        "IMPORTANT:\n"
                        "- Your reply will be forwarded exactly as-is. Do NOT include any notes, comments, or explanation.\n"
                        "- This is direct communication with the lead — your output must sound natural and one-to-one.\n"
                        "- Do not make up information — only refer to what is clearly stated in the transcript.\n"
                        "- If details are missing, leave them out. Never guess or fill in gaps.\n"
                        "- Do not include subject lines or internal instructions.\n"
                        "- Include this booking link at the end: https://calendly.com/dromel/30min"
                    )
                },
                {
                    "role": "user",
                    "content": f"Transcript: {transcript or 'A discussion about how our AI calling system can help improve lead conversion rates.'}"
                }
            ],
            temperature=0.7,
            max_tokens=300
                        )

            sms_content = response.choices[0].message.content.strip()

            # Ensure the SMS is under 155 characters
            if sms_content:
                return sms_content
            
        except Exception as e:
            print(f"Error generating SMS content: {e}")
            
        # Fallback message if generation fails
        return None
        
    def generate_reminder_email_content(self, first_name: str = None, last_name: str = None, transcript: str = None) -> str:
        """
        Generate a personalized reminder email based on call transcript
        Returns a short, urgent reminder email (2-4 sentences) in Gary Halbert style,
        creating a sense of urgency and encouraging immediate action.

        Args:
            first_name (str): Recipient's first name
            last_name (str): Recipient's last name
            transcript (str): Call transcript to extract key points

        Returns:
            str: Personalized reminder email content
        """
        print("Generating personalized reminder email content...")

        # Create name for personalization
        name = first_name or last_name or ""

        try:
            response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": (
                        "You are writing a REMINDER EMAIL on behalf of CapVox, an AI follow-up automation "
                        "platform built for MCA companies.\n\n"
                        "Your job is to create a gentle but effective reminder that references the previous call "
                        "the prospect had with an AI demo. This is a FOLLOW-UP to an email they already received.\n\n"
                        "Key reminders:\n"
                        "- Create a sense of gentle urgency\n"
                        "- Mention this is a quick reminder about our previous conversation\n"
                        "- Reference 1-2 specific value points from their call\n"
                        "- Be concise, friendly, and straight to the point\n"
                        "- End with a clear call to action\n\n"
                        "Write a short, personalized reminder email (2-4 sentences) in the Gary Halbert copywriting style "
                        "that creates a sense of urgency without being pushy. Make it feel like it was written by a "
                        "helpful, attentive professional who genuinely wants to help.\n\n"
                        "IMPORTANT:\n"
                        "- Begin with 'Just a friendly reminder about our conversation.'\n"
                        "- Your reply will be forwarded exactly as-is. Do NOT include any notes, comments, or explanation.\n"
                        "- This is direct communication with the lead — your output must sound natural and one-to-one.\n"
                        "- Do not make up information — only refer to what is clearly stated in the transcript.\n"
                        "- If details are missing, leave them out. Never guess or fill in gaps.\n"
                        "- Do not include subject lines or internal instructions.\n"
                        "- Include this booking link at the end: https://calendly.com/dromel/30min\n"
                        "- Close the email with: Looking forward to connecting, CapVox AI"
                    )
                },
                {
                    "role": "user",
                    "content": f"Reminder for {name}: {transcript or 'A discussion about how our AI calling system can help improve lead conversion rates and follow-up efficiency.'}"
                }
            ],
            temperature=0.7,
            max_tokens=300
            )

            reminder_content = response.choices[0].message.content.strip()

            if reminder_content:
                return reminder_content
            

        except Exception as e:
            print(f"Error generating reminder email content: {e}")
            # Fallback content if exception occurs
            return None               
                
# Example usage
# if __name__ == "__main__":
#     processor = CompanyResearchProcessor()
    
#     # Example company data (similar to what would come from the database)
#     test_company = {
#         "company_name": "Tech Solutions Inc.",
#         "website": "https://techsolutions.example.com",
#         "company_linkedin_url": "https://linkedin.com/company/techsolutions",
#         "industry": "Information Technology",
#         "seo_description": "Tech Solutions provides cutting-edge IT services and solutions for businesses of all sizes.",
#         "technologies": "Python, React, AWS, Docker"
#     }
    
#     # Process company and generate prompt
#     prompt = processor.process_company(test_company)
    
#     # Test sentiment analysis
#     test_summary = "The call went well. The customer was satisfied with the service and plans to renew their subscription."
#     sentiment = processor.analyze_call_sentiment(test_summary)
#     print(f"Sentiment analysis result: {sentiment}")